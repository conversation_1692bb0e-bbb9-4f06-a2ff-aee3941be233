<div class="pad">
  <div class="back-title-container">
    <span class="back-arrow" (click)="onBack()">
      ←
    </span>
    <h2 class="page-title">Participation à l'Événement Médical</h2>
  </div>

  <div class="container mt-4">
    <div class="row justify-content-center">
      <div class="col-lg-10 col-md-12">
        <mat-card class="p-4">
          <mat-card-header class="mb-4">
            <mat-card-title class="text-center">
              Formulaire de Participation - Congrès Médical International 2025
            </mat-card-title>
            <mat-card-subtitle class="text-center mt-2">
              Veuillez remplir toutes les informations requises pour valider votre participation
            </mat-card-subtitle>
          </mat-card-header>
          
          <mat-card-content>
            <form [formGroup]="participationForm" (ngSubmit)="onSubmit()" class="participation-form">
              
              <!-- Section Informations Personnelles -->
              <div class="form-section mb-5">
                <h4 class="section-title mb-4">
                  <mat-icon class="section-icon">person</mat-icon>
                  Informations Personnelles
                </h4>
                
                <div class="row">
                  <div class="col-md-6 mb-3">
                    <mat-form-field appearance="outline" class="w-100">
                      <mat-label>Nom *</mat-label>
                      <input matInput formControlName="nom" placeholder="Votre nom de famille">
                      <mat-error *ngIf="nom?.hasError('required')">Le nom est requis</mat-error>
                      <mat-error *ngIf="nom?.hasError('minlength')">Le nom doit contenir au moins 2 caractères</mat-error>
                    </mat-form-field>
                  </div>
                  
                  <div class="col-md-6 mb-3">
                    <mat-form-field appearance="outline" class="w-100">
                      <mat-label>Prénom *</mat-label>
                      <input matInput formControlName="prenom" placeholder="Votre prénom">
                      <mat-error *ngIf="prenom?.hasError('required')">Le prénom est requis</mat-error>
                      <mat-error *ngIf="prenom?.hasError('minlength')">Le prénom doit contenir au moins 2 caractères</mat-error>
                    </mat-form-field>
                  </div>
                </div>

                <div class="row">
                  <div class="col-md-6 mb-3">
                    <mat-form-field appearance="outline" class="w-100">
                      <mat-label>Email *</mat-label>
                      <input matInput type="email" formControlName="email" placeholder="<EMAIL>">
                      <mat-error *ngIf="email?.hasError('required')">L'email est requis</mat-error>
                      <mat-error *ngIf="email?.hasError('email')">Format d'email invalide</mat-error>
                    </mat-form-field>
                  </div>
                  
                  <div class="col-md-6 mb-3">
                    <mat-form-field appearance="outline" class="w-100">
                      <mat-label>Téléphone *</mat-label>
                      <input matInput formControlName="telephone" placeholder="0612345678">
                      <mat-error *ngIf="telephone?.hasError('required')">Le téléphone est requis</mat-error>
                      <mat-error *ngIf="telephone?.hasError('pattern')">Format invalide (10 chiffres)</mat-error>
                    </mat-form-field>
                  </div>
                </div>

                <div class="row">
                  <div class="col-md-6 mb-3">
                    <mat-form-field appearance="outline" class="w-100">
                      <mat-label>Date de naissance *</mat-label>
                      <input matInput [matDatepicker]="picker1" formControlName="dateNaissance">
                      <mat-datepicker-toggle matIconSuffix [for]="picker1"></mat-datepicker-toggle>
                      <mat-datepicker #picker1></mat-datepicker>
                      <mat-error *ngIf="participationForm.get('dateNaissance')?.hasError('required')">
                        La date de naissance est requise
                      </mat-error>
                    </mat-form-field>
                  </div>
                  
                  <div class="col-md-6 mb-3">
                    <label class="form-label fw-bold">Sexe *</label>
                    <mat-radio-group formControlName="sexe" class="d-flex gap-4 mt-2">
                      <mat-radio-button value="masculin">Masculin</mat-radio-button>
                      <mat-radio-button value="feminin">Féminin</mat-radio-button>
                    </mat-radio-group>
                    <mat-error *ngIf="participationForm.get('sexe')?.hasError('required') && participationForm.get('sexe')?.touched">
                      Le sexe est requis
                    </mat-error>
                  </div>
                </div>
              </div>

              <!-- Section Informations Professionnelles -->
              <div class="form-section mb-5">
                <h4 class="section-title mb-4">
                  <mat-icon class="section-icon">work</mat-icon>
                  Informations Professionnelles
                </h4>
                
                <div class="row">
                  <div class="col-md-6 mb-3">
                    <mat-form-field appearance="outline" class="w-100">
                      <mat-label>Profession *</mat-label>
                      <mat-select formControlName="profession">
                        <mat-option value="medecin">Médecin</mat-option>
                        <mat-option value="infirmier">Infirmier(ère)</mat-option>
                        <mat-option value="pharmacien">Pharmacien(ne)</mat-option>
                        <mat-option value="dentiste">Dentiste</mat-option>
                        <mat-option value="kinesitherapeute">Kinésithérapeute</mat-option>
                        <mat-option value="sage_femme">Sage-femme</mat-option>
                        <mat-option value="etudiant">Étudiant en médecine</mat-option>
                        <mat-option value="autre">Autre</mat-option>
                      </mat-select>
                      <mat-error *ngIf="profession?.hasError('required')">La profession est requise</mat-error>
                    </mat-form-field>
                  </div>
                  
                  <div class="col-md-6 mb-3">
                    <mat-form-field appearance="outline" class="w-100">
                      <mat-label>Spécialité *</mat-label>
                      <mat-select formControlName="specialite">
                        <mat-option *ngFor="let spec of specialites" [value]="spec.value">
                          {{spec.label}}
                        </mat-option>
                      </mat-select>
                      <mat-error *ngIf="specialite?.hasError('required')">La spécialité est requise</mat-error>
                    </mat-form-field>
                  </div>
                </div>

                <div class="row">
                  <div class="col-md-8 mb-3">
                    <mat-form-field appearance="outline" class="w-100">
                      <mat-label>Établissement *</mat-label>
                      <input matInput formControlName="etablissement" placeholder="Nom de votre établissement">
                      <mat-error *ngIf="etablissement?.hasError('required')">L'établissement est requis</mat-error>
                    </mat-form-field>
                  </div>
                  
                  <div class="col-md-4 mb-3">
                    <mat-form-field appearance="outline" class="w-100">
                      <mat-label>Années d'expérience *</mat-label>
                      <input matInput type="number" formControlName="anneesExperience" min="0" max="50">
                      <mat-error *ngIf="participationForm.get('anneesExperience')?.hasError('required')">
                        Les années d'expérience sont requises
                      </mat-error>
                    </mat-form-field>
                  </div>
                </div>

                <div class="row">
                  <div class="col-12 mb-3">
                    <mat-form-field appearance="outline" class="w-100">
                      <mat-label>Adresse de l'établissement *</mat-label>
                      <textarea matInput formControlName="adresseEtablissement" 
                                placeholder="Adresse complète de votre établissement" rows="2"></textarea>
                      <mat-error *ngIf="participationForm.get('adresseEtablissement')?.hasError('required')">
                        L'adresse de l'établissement est requise
                      </mat-error>
                    </mat-form-field>
                  </div>
                </div>
              </div>

              <!-- Section Participation -->
              <div class="form-section mb-5">
                <h4 class="section-title mb-4">
                  <mat-icon class="section-icon">event</mat-icon>
                  Détails de Participation
                </h4>
                
                <div class="row">
                  <div class="col-12 mb-3">
                    <mat-form-field appearance="outline" class="w-100">
                      <mat-label>Type de participation *</mat-label>
                      <mat-select formControlName="typeParticipation">
                        <mat-option *ngFor="let type of typesParticipation" [value]="type.value">
                          {{type.label}}
                        </mat-option>
                      </mat-select>
                      <mat-error *ngIf="participationForm.get('typeParticipation')?.hasError('required')">
                        Le type de participation est requis
                      </mat-error>
                    </mat-form-field>
                  </div>
                </div>

                <div class="row">
                  <div class="col-12 mb-3">
                    <mat-form-field appearance="outline" class="w-100">
                      <mat-label>Motivations pour participer *</mat-label>
                      <textarea matInput formControlName="motivations" 
                                placeholder="Expliquez vos motivations pour participer à cet événement (minimum 50 caractères)" 
                                rows="4"></textarea>
                      <mat-hint>{{motivations?.value?.length || 0}}/50 caractères minimum</mat-hint>
                      <mat-error *ngIf="motivations?.hasError('required')">Les motivations sont requises</mat-error>
                      <mat-error *ngIf="motivations?.hasError('minlength')">
                        Minimum 50 caractères requis
                      </mat-error>
                    </mat-form-field>
                  </div>
                </div>

                <div class="row">
                  <div class="col-12 mb-3">
                    <mat-form-field appearance="outline" class="w-100">
                      <mat-label>Attentes de l'événement *</mat-label>
                      <textarea matInput formControlName="attentes" 
                                placeholder="Quelles sont vos attentes concernant cet événement ? (minimum 30 caractères)" 
                                rows="3"></textarea>
                      <mat-hint>{{attentes?.value?.length || 0}}/30 caractères minimum</mat-hint>
                      <mat-error *ngIf="attentes?.hasError('required')">Les attentes sont requises</mat-error>
                      <mat-error *ngIf="attentes?.hasError('minlength')">
                        Minimum 30 caractères requis
                      </mat-error>
                    </mat-form-field>
                  </div>
                </div>
              </div>

              <!-- Section Disponibilité -->
              <div class="form-section mb-5">
                <h4 class="section-title mb-4">
                  <mat-icon class="section-icon">schedule</mat-icon>
                  Disponibilité
                </h4>
                
                <div class="row">
                  <div class="col-12 mb-3">
                    <mat-checkbox formControlName="disponibiliteComplete" class="mb-3">
                      Je confirme ma disponibilité complète pour toute la durée de l'événement
                    </mat-checkbox>
                  </div>
                </div>

                <div class="row">
                  <div class="col-12 mb-3">
                    <mat-form-field appearance="outline" class="w-100">
                      <mat-label>Dates d'indisponibilité (optionnel)</mat-label>
                      <textarea matInput formControlName="datesIndisponibles" 
                                placeholder="Mentionnez les dates où vous ne seriez pas disponible" 
                                rows="2"></textarea>
                    </mat-form-field>
                  </div>
                </div>
              </div>

              <!-- Section Besoins Spéciaux -->
              <div class="form-section mb-5">
                <h4 class="section-title mb-4">
                  <mat-icon class="section-icon">accessibility</mat-icon>
                  Besoins Particuliers
                </h4>
                
                <div class="row">
                  <div class="col-md-6 mb-3">
                    <mat-form-field appearance="outline" class="w-100">
                      <mat-label>Besoins d'accessibilité (optionnel)</mat-label>
                      <textarea matInput formControlName="besoinsSpeciaux" 
                                placeholder="Mentionnez vos besoins particuliers d'accessibilité" 
                                rows="3"></textarea>
                    </mat-form-field>
                  </div>
                  
                  <div class="col-md-6 mb-3">
                    <mat-form-field appearance="outline" class="w-100">
                      <mat-label>Régime alimentaire (optionnel)</mat-label>
                      <mat-select formControlName="regimeAlimentaire">
                        <mat-option value="">Aucun régime particulier</mat-option>
                        <mat-option value="vegetarien">Végétarien</mat-option>
                        <mat-option value="vegan">Végan</mat-option>
                        <mat-option value="halal">Halal</mat-option>
                        <mat-option value="kasher">Kasher</mat-option>
                        <mat-option value="sans_gluten">Sans gluten</mat-option>
                        <mat-option value="autre">Autre</mat-option>
                      </mat-select>
                    </mat-form-field>
                  </div>
                </div>
              </div>

              <!-- Section Documents -->
              <div class="form-section mb-5">
                <h4 class="section-title mb-4">
                  <mat-icon class="section-icon">upload_file</mat-icon>
                  Documents Justificatifs
                </h4>
                
                <div class="upload-section">
                  <!-- CV -->
                  <div class="upload-item mb-3">
                    <button type="button" mat-stroked-button class="upload-btn w-100" 
                            (click)="fileInputCV.click()">
                      <mat-icon>upload</mat-icon>
                      Téléverser votre CV
                    </button>
                    <input #fileInputCV type="file" hidden accept=".pdf,.doc,.docx" 
                           (change)="onFileSelect($event, 'cv')">
                    <div *ngIf="uploadedFiles.cv" class="file-info mt-2 d-flex justify-content-between align-items-center">
                      <small class="text-success">
                        <mat-icon class="small-icon">check_circle</mat-icon>
                        {{uploadedFiles.cv.name}}
                      </small>
                      <button type="button" mat-icon-button color="warn" 
                              (click)="removeFile('cv')" class="small-btn">
                        <mat-icon>delete</mat-icon>
                      </button>
                    </div>
                  </div>

                  <!-- Diplôme -->
                  <div class="upload-item mb-3">
                    <button type="button" mat-stroked-button class="upload-btn w-100" 
                            (click)="fileInputDiploma.click()">
                      <mat-icon>upload</mat-icon>
                      Téléverser copie du diplôme
                    </button>
                    <input #fileInputDiploma type="file" hidden accept=".pdf,.jpg,.jpeg,.png" 
                           (change)="onFileSelect($event, 'diploma')">
                    <div *ngIf="uploadedFiles.diploma" class="file-info mt-2 d-flex justify-content-between align-items-center">
                      <small class="text-success">
                        <mat-icon class="small-icon">check_circle</mat-icon>
                        {{uploadedFiles.diploma.name}}
                      </small>
                      <button type="button" mat-icon-button color="warn" 
                              (click)="removeFile('diploma')" class="small-btn">
                        <mat-icon>delete</mat-icon>
                      </button>
                    </div>
                  </div>

                  <!-- Licence professionnelle -->
                  <div class="upload-item mb-3">
                    <button type="button" mat-stroked-button class="upload-btn w-100" 
                            (click)="fileInputLicense.click()">
                      <mat-icon>upload</mat-icon>
                      Téléverser licence professionnelle
                    </button>
                    <input #fileInputLicense type="file" hidden accept=".pdf,.jpg,.jpeg,.png" 
                           (change)="onFileSelect($event, 'license')">
                    <div *ngIf="uploadedFiles.license" class="file-info mt-2 d-flex justify-content-between align-items-center">
                      <small class="text-success">
                        <mat-icon class="small-icon">check_circle</mat-icon>
                        {{uploadedFiles.license.name}}
                      </small>
                      <button type="button" mat-icon-button color="warn" 
                              (click)="removeFile('license')" class="small-btn">
                        <mat-icon>delete</mat-icon>
                      </button>
                    </div>
                  </div>

                  <!-- Photo -->
                  <div class="upload-item mb-3">
                    <button type="button" mat-stroked-button class="upload-btn w-100" 
                            (click)="fileInputPhoto.click()">
                      <mat-icon>upload</mat-icon>
                      Téléverser photo d'identité
                    </button>
                    <input #fileInputPhoto type="file" hidden accept=".jpg,.jpeg,.png" 
                           (change)="onFileSelect($event, 'photo')">
                    <div *ngIf="uploadedFiles.photo" class="file-info mt-2 d-flex justify-content-between align-items-center">
                      <small class="text-success">
                        <mat-icon class="small-icon">check_circle</mat-icon>
                        {{uploadedFiles.photo.name}}
                      </small>
                      <button type="button" mat-icon-button color="warn" 
                              (click)="removeFile('photo')" class="small-btn">
                        <mat-icon>delete</mat-icon>
                      </button>
                    </div>
                  </div>
                </div>
              </div>

              <!-- Section Acceptation -->
              <div class="form-section mb-4">
                <h4 class="section-title mb-4">
                  <mat-icon class="section-icon">rule</mat-icon>
                  Conditions et Acceptation
                </h4>
                
                <div class="conditions-section p-3 mb-3">
                  <mat-checkbox formControlName="accepteConditions" class="mb-3">
                    <span class="fw-bold">J'accepte les conditions de participation *</span>
                  </mat-checkbox>
                  <mat-error *ngIf="accepteConditions?.hasError('required') && accepteConditions?.touched">
                    Vous devez accepter les conditions de participation
                  </mat-error>
                  
                  <mat-checkbox formControlName="accepteNewsletter" class="mt-2">
                    J'accepte de recevoir la newsletter et les informations sur les futures événements
                  </mat-checkbox>
                </div>
              </div>

              <!-- Bouton de soumission -->
              <div class="row">
                <div class="col-12 text-center">
                  <button type="submit" mat-raised-button color="primary" 
                          class="submit-btn px-5 py-2"
                          [disabled]="!participationForm.valid">
                    <mat-icon>send</mat-icon>
                    Soumettre ma candidature
                  </button>
                </div>
              </div>

            </form>
          </mat-card-content>
        </mat-card>
      </div>
    </div>
  </div>
</div>