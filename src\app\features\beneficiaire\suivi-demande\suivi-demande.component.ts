import { Component , OnInit } from '@angular/core';
import { MatButtonModule } from '@angular/material/button';
import { MatCardModule } from '@angular/material/card';
import { MatIconModule } from '@angular/material/icon';
import { MatDividerModule } from '@angular/material/divider';
import { MatTableModule } from '@angular/material/table';
import { MatChipsModule } from '@angular/material/chips';
import { CommonModule } from '@angular/common';

@Component({
  selector: 'app-suivi-demande',
  imports: [ 
    MatButtonModule,
    MatCardModule,
    MatIconModule,
    MatDividerModule,
    MatTableModule,
    MatChipsModule,
    CommonModule
  ],
  templateUrl: './suivi-demande.component.html',
  styleUrl: './suivi-demande.component.scss'
})
export class SuiviDemandeComponent implements OnInit{

      demandes = [
    {
      id: '38',
      materiel: 'Lunettes',
      statut: 'EN-COURS',
      statutLibelle: 'En cours'
    },
    {
      id: '02',
      materiel: 'Appareil auditif',
      statut: 'ACCEPTE',
      statutLibelle: 'Accepté'
    },
    {
      id: '03',
      materiel: 'Canne à pêche',
      statut: 'REFUSE',
      statutLibelle: 'Refusé'
    },
        {
      id: '109',
      materiel: 'Lunettes',
      statut: 'LIVRE',
      statutLibelle: 'livré'
    }
  ];

  constructor() { }

  ngOnInit(): void {
  }

  getStatusClass(statut: string): string {
    switch (statut) {
      case 'EN_COURS':
        return 'status-en-cours';
      case 'ACCEPTE':
        return 'status-accepte';
      case 'REFUSE':
        return 'status-refuse';
      default:
        return '';
    }
  }
  onBack() {
  // Par exemple : revenir à la page précédente
  history.back(); // ou this.router.navigate(['/autre-page']);
}

}
