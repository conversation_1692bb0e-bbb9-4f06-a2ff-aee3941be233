import { Routes } from '@angular/router';
// Layouts
import { PublicLayoutComponent } from './layouts/public-layout/public-layout.component';
import { AdminLayoutComponent } from './layouts/admin-layout/admin-layout.component';
import { BeneficiaireLayoutComponent } from './layouts/beneficiaire-layout/beneficiaire-layout.component';


export const routes: Routes = [
      {
    path: '',
    component: PublicLayoutComponent,
    children: [
      { path: '', redirectTo: 'home', pathMatch: 'full' },
      {
        path: 'home',
        loadComponent: () => import('./features/page-acceuil/home/<USER>').then(m => m.HomeComponent),
      },
      {
        path: 'a-propos',
        loadComponent: () => import('./features/page-acceuil/a-propos/a-propos.component').then(m => m.AProposComponent),
      },
      {
        path: 'login',
        loadComponent: () => import('./features/authentification/login/login.component').then(m => m.LoginComponent),
      },
      {
        path: 'register',
        loadComponent: () => import('./features/authentification/register/register.component').then(m => m.RegisterComponent),
      },
      {
        path: 'forgot-password',
        loadComponent: () => import('./features/authentification/forgot-password/forgot-password.component').then(m => m.ForgotPasswordComponent),
      },
      {
        path: 'reset-password',
        loadComponent: () => import('./features/authentification/reset-password/reset-password.component').then(m => m.ResetPasswordComponent),
      }
    ]
  },

  {
    path: 'admin',
    component: AdminLayoutComponent,
    children: [
      {
        path: 'admin-home',
        loadComponent: () => import('./features/admin/admin-home/admin-home.component').then(m => m.AdminHomeComponent),
      },
      {
        path: 'gestion-stock',
        loadComponent: () => import('./features/admin/gestion-stock/gestion-stock.component').then(m => m.GestionStockComponent),
      },
            {
        path: 'gestion-evenements',
        loadComponent: () => import('./features/admin/gestion-evenements/gestion-evenements.component').then(m => m.GestionEvenementsComponent),
      },
      {
        path: 'validation-demandes',
        loadComponent: () => import('./features/admin/validation-demandes/validation-demandes.component').then(m => m.ValidationDemandesComponent),
      },
      {
        path: 'gestion-beneficiaires',
        loadComponent: () => import('./features/admin/gestion-beneficiaires/gestion-beneficiaires.component').then(m => m.GestionBeneficiairesComponent),
      },
      {
        path: 'change-password',
        loadComponent: () => import('./features/admin/change-password/change-password.component').then(m => m.ChangePasswordComponent),
      }
    ]
  },

  {
    path: 'beneficiaire',
    component: BeneficiaireLayoutComponent,
    children: [
      {
        path: 'beneficiaire-home',
        loadComponent: () => import('./features/beneficiaire/beneficiaire-home/beneficiaire-home.component').then(m => m.BeneficiaireHomeComponent),
      },
      {
        path: 'demandes',
        loadComponent: () => import('./features/beneficiaire/demandes/demandes.component').then(m => m.DemandesComponent),
      },
      {
        path: 'documents',
        loadComponent: () => import('./features/beneficiaire/documents/documents.component').then(m => m.DocumentsComponent),
      },
      {
        path: 'suivi-demande',
        loadComponent: () => import('./features/beneficiaire/suivi-demande/suivi-demande.component').then(m => m.SuiviDemandeComponent),
      },
      {
        path: 'change-password',
        loadComponent: () => import('./features/beneficiaire/change-password/change-password.component').then(m => m.ChangePasswordComponent),
      },
      {
        path: 'participation',
        loadComponent: () => import('./features/beneficiaire/participation/participation.component').then(m => m.ParticipationComponent),
      }
    ]
  },

  // Route fallback (erreur 404)
  {
    path: '**',
    redirectTo: ''
  }
];
