.auth-container {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: auto;
  background-color: transparent;
  padding: 20px 20px 1rem 20px;
  margin-top: 7rem;
  margin-bottom: 1rem;
}

.auth-card {
  display: flex;
  width: 900px;
  max-width: 100%;
  min-height: 600px;
  background-color: white;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
}

.background-image {
  flex: 1;
  background-image: linear-gradient(0deg, #43cea2 0%, #185a9d 100%);
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 20px;
  color: white;
  
  h1 {
    font-size: 48px;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
  }
}

.form-container {
  flex: 1;
  padding: 40px;
  position: relative;
}

.back-button {
  position: absolute;
  top: 20px;
  left: 20px;
  background: none;
  border: none;
  font-size: 24px;
  cursor: pointer;
  color: #555;
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  background-color: #f5f5f5;
  text-decoration: none;
}

h2 {
  font-size: 28px;
  margin-top: 40px;
  margin-bottom: 8px;
  color: #333;
}

.subtitle {
  color: #777;
  margin-bottom: 30px;
}

.form-group {
  margin-bottom: 20px;
  
  label {
    display: block;
    margin-bottom: 8px;
    color: #555;
    font-size: 14px;
  }
  
  input {
    width: 100%;
    padding: 12px 15px;
    border: 1px solid #ddd;
    border-radius: 6px;
    font-size: 15px;
    outline: none;
    transition: border-color 0.3s;

    &:focus {
      border-color: #43cea2;
    }

    // Désactiver l'autocomplétion du navigateur
    &:-webkit-autofill,
    &:-webkit-autofill:hover,
    &:-webkit-autofill:focus,
    &:-webkit-autofill:active {
      -webkit-box-shadow: 0 0 0 30px white inset !important;
      -webkit-text-fill-color: #333 !important;
      transition: background-color 5000s ease-in-out 0s;
    }
  }
  
  .error-message {
    color: #dc3545;
    font-size: 0.875rem;
    margin-top: 0.25rem;
  }
}

.password-input {
  position: relative;
  display: flex;
  align-items: center;

  input {
    flex: 1;
  }

  .toggle-password {
    position: absolute;
    right: 0.5rem;
    background: none;
    border: none;
    color: #666;
    cursor: pointer;
    padding: 0.25rem;

    &:hover {
      color: #333;
    }

    mat-icon {
      font-size: 1.25rem;
    }
  }
}

.error-alert {
  display: flex;
  align-items: center;
  background-color: #fff5f5;
  border: 1px solid #fed7d7;
  border-radius: 0.375rem;
  color: #dc3545;
  padding: 0.75rem;
  margin-bottom: 1rem;

  .error-icon {
    margin-right: 0.5rem;
    font-size: 1.25rem;
  }
}

.submit-button {
  width: 100%;
  padding: 14px;
  background-color: #43cea2;
  color: white;
  border: none;
  border-radius: 6px;
  font-size: 16px;
  font-weight: bold;
  cursor: pointer;
  transition: background-color 0.3s;
  
  &:hover {
    background-color: #35bb90;
  }
  
  &:disabled {
    background-color: #a8e0d0;
    cursor: not-allowed;
  }
}

.auth-links {
  margin-top: 25px;
  text-align: center;
  font-size: 14px;
  color: #777;
  
  a {
    color: #43cea2;
    text-decoration: none;
    font-weight: bold;
    cursor: pointer;
    
    &:hover {
      text-decoration: underline;
    }
  }
}

@media (max-width: 768px) {
  .auth-card {
    flex-direction: column;
  }
  
  .background-image {
    min-height: 150px;
  }
}
