export interface Beneficiaire {
  id: number;
  nom: string;
  prenom: string;
  cin: string;
  dateNaissance: Date;
  adresse: string;
  telephone: string;
  email: string;
  statutSocial: string;
  justificatif?: string;
}

export class BeneficiaireModel implements Beneficiaire {
  constructor(
    public id: number,
    public nom: string,
    public prenom: string,
    public cin: string,
    public dateNaissance: Date,
    public adresse: string,
    public telephone: string,
    public email: string,
    public statutSocial: string,
    public justificatif?: string
  ) {}

  getAge(): number {
    const today = new Date();
    const birthDate = new Date(this.dateNaissance);
    let age = today.getFullYear() - birthDate.getFullYear();
    const m = today.getMonth() - birthDate.getMonth();
    if (m < 0 || (m === 0 && today.getDate() < birthDate.getDate())) {
      age--;
    }
    return age;
  }

  getFullName(): string {
    return `${this.prenom} ${this.nom}`;
  }
}
