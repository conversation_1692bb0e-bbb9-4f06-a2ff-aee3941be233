.pad{
        margin-top: 7rem;
        background-color: transparent;
    }
.back-title-container {
  display: flex;
  align-items: center;
  margin-bottom: 1rem;
  gap: 0.5rem;
  padding: 0 2rem;
  margin-top: 3rem;
}

.back-arrow {
  font-size: 1.5rem;
  cursor: pointer;
  color: #3498db;
  transition: color 0.3s;

  &:hover {
    color: #2ecc71;
  }
}

.page-title {
  font-size: 1.5rem;
  font-weight: 600;
  color: #2c3e50;
  margin: 0;
}
    .device-form {
      max-width: 100%;
    }

    .mat-mdc-form-field {
      width: 100%;
    }

    .mat-mdc-radio-group {
      display: flex;
      gap: 2rem;
    }

    .upload-section {
      background-color: #f8f9fa;
      padding: 1.5rem;
      border-radius: 8px;
      border: 2px dashed #dee2e6;
    }

    .upload-btn {
      height: 50px;
      border: 1px solid #ddd;
      background-color: white;
      color: #666;
      border-radius: 4px;
      display: flex;
      align-items: center;
      justify-content: center;
      gap: 0.5rem;
      transition: all 0.3s ease;
    }

    .upload-btn:hover {
      background-color: #f8f9fa;
      border-color: #007bff;
    }

    .file-info {
      display: flex;
      align-items: center;
      gap: 0.5rem;
    }

    .small-icon {
      font-size: 16px;
      width: 16px;
      height: 16px;
    }

    .submit-btn {
      background-color: rgb(7, 211, 167) !important;
      color: white;
      min-width: 120px;
      height: 45px;
      font-size: 16px;
      font-weight: 500;
    }

    .submit-btn:disabled {
      background-color: #6c757d !important;
    }

    mat-card {
      box-shadow: 0 4px 8px rgba(0,0,0,0.1);
    }

    .form-label {
      color: #495057;
      margin-bottom: 0.5rem;
    }

    @media (max-width: 768px) {
      .container {
        padding: 1rem;
      }
      
      .mat-mdc-radio-group {
        flex-direction: column;
        gap: 1rem;
      }
    }