import { Component , OnInit} from '@angular/core';
import { CommonModule } from '@angular/common';
import { MatCardModule } from '@angular/material/card';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatChipsModule } from '@angular/material/chips';
import { MatDividerModule } from '@angular/material/divider';
import { MatDialogModule, MatDialog } from '@angular/material/dialog';
import { MatSnackBarModule, MatSnackBar } from '@angular/material/snack-bar';
import { MatBadgeModule } from '@angular/material/badge';

interface Demande {
  id: number;
  nom: string;
  prenom: string;
  email: string;
  sexe: string;
  adresse: string;
  dateNaissance: string;
  statut: string;
  couverture: string;
  type: string;
  documents: {
    cin: string;
    amo: string;
    certificatMedical: string;
    certificatHandicape?: string;
  };
  dateSubmission: Date;
  statusValidation: 'en_attente' | 'approuve' | 'rejete';
}

@Component({
  selector: 'app-validation-demandes',
  imports: [ CommonModule, MatCardModule,
     MatButtonModule, MatIconModule, 
     MatChipsModule, MatDividerModule, 
     MatDialogModule, MatSnackBarModule, 
     MatBadgeModule],
  templateUrl: './validation-demandes.component.html',
  styleUrl: './validation-demandes.component.scss'
})
export class ValidationDemandesComponent implements OnInit{

  demandes: Demande[] = [];
  demandesEnAttente: Demande[] = [];
  demandesApprouvees: Demande[] = [];
  demandesRejetees: Demande[] = [];

  constructor(private snackBar: MatSnackBar) {}

  ngOnInit() {
    this.loadDemandes();
  }

  loadDemandes() {
    // Données d'exemple - en réalité, ces données viendraient d'un service
    this.demandes = [
      {
        id: 1,
        nom: 'BENALI',
        prenom: 'Ahmed',
        email: '<EMAIL>',
        sexe: 'Masculin',
        adresse: '123 Rue Mohammed V, Casablanca',
        dateNaissance: '15/03/1985',
        statut: 'Ayant droit',
        couverture: 'CNOPS',
        type: 'Consultation générale',
        documents: {
          cin: 'cin_ahmed_benali.pdf',
          amo: 'amo_ahmed_benali.pdf',
          certificatMedical: 'cert_med_ahmed_benali.pdf'
        },
        dateSubmission: new Date('2024-05-30T10:30:00'),
        statusValidation: 'en_attente'
      },
      {
        id: 2,
        nom: 'ALAMI',
        prenom: 'Fatima',
        email: '<EMAIL>',
        sexe: 'Féminin',
        adresse: '456 Avenue Hassan II, Rabat',
        dateNaissance: '22/07/1990',
        statut: 'Assuré AMO',
        couverture: 'CNSS',
        type: 'Consultation spécialisée',
        documents: {
          cin: 'cin_fatima_alami.pdf',
          amo: 'amo_fatima_alami.pdf',
          certificatMedical: 'cert_med_fatima_alami.pdf',
          certificatHandicape: 'cert_handicap_fatima_alami.pdf'
        },
        dateSubmission: new Date('2024-05-29T14:15:00'),
        statusValidation: 'approuve'
      },
      {
        id: 3,
        nom: 'TAHIRI',
        prenom: 'Youssef',
        email: '<EMAIL>',
        sexe: 'Masculin',
        adresse: '789 Rue Ibn Battuta, Fès',
        dateNaissance: '10/12/1975',
        statut: 'Ayant droit',
        couverture: 'RAMED',
        type: 'Urgence',
        documents: {
          cin: 'cin_youssef_tahiri.pdf',
          amo: 'amo_youssef_tahiri.pdf',
          certificatMedical: 'cert_med_youssef_tahiri.pdf'
        },
        dateSubmission: new Date('2024-05-28T09:45:00'),
        statusValidation: 'rejete'
      }
    ];

    this.updateStatistics();
  }

  updateStatistics() {
    this.demandesEnAttente = this.demandes.filter(d => d.statusValidation === 'en_attente');
    this.demandesApprouvees = this.demandes.filter(d => d.statusValidation === 'approuve');
    this.demandesRejetees = this.demandes.filter(d => d.statusValidation === 'rejete');
  }

  getStatusClass(status: string): string {
    return status.replace('_', '-');
  }

  getStatusChipClass(status: string): string {
    return `status-chip-${status.replace('_', '-')}`;
  }

  getStatusIcon(status: string): string {
    switch (status) {
      case 'en_attente': return 'pending';
      case 'approuve': return 'check_circle';
      case 'rejete': return 'cancel';
      default: return 'help';
    }
  }

  getStatusText(status: string): string {
    switch (status) {
      case 'en_attente': return 'En attente';
      case 'approuve': return 'Approuvée';
      case 'rejete': return 'Rejetée';
      default: return 'Inconnu';
    }
  }

  voirDocument(documentName: string) {
    // En réalité, ceci ouvrirait le document dans un viewer ou téléchargerait le fichier
    this.snackBar.open(`Ouverture du document: ${documentName}`, 'Fermer', {
      duration: 3000
    });
  }

  approuverDemande(demande: Demande) {
    demande.statusValidation = 'approuve';
    this.updateStatistics();
    this.snackBar.open(`Demande de ${demande.prenom} ${demande.nom} approuvée avec succès!`, 'Fermer', {
      duration: 4000,
      panelClass: ['success-snackbar']
    });
  }

  rejeterDemande(demande: Demande) {
    demande.statusValidation = 'rejete';
    this.updateStatistics();
    this.snackBar.open(`Demande de ${demande.prenom} ${demande.nom} rejetée.`, 'Fermer', {
      duration: 4000,
      panelClass: ['error-snackbar']
    });
  }

}
