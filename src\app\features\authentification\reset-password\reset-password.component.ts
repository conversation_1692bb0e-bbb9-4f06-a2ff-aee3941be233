import { Component } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormBuilder, FormGroup, Validators , ReactiveFormsModule} from '@angular/forms';
import { Router } from '@angular/router';
// import { AuthService } from '../../../core/auth/auth.service';

@Component({
  selector: 'app-reset-password',
  standalone : true,
  imports: [ CommonModule , ReactiveFormsModule ],
  templateUrl: './reset-password.component.html',
  styleUrl: './reset-password.component.scss'
})
export class ResetPasswordComponent {

    forgotPasswordForm: FormGroup;

  constructor(private fb: FormBuilder, private router: Router 
    // , private authService: AuthService
  ) {
    this.forgotPasswordForm = this.fb.group({
      email: ['', [Validators.required, Validators.email]]
    });
  }

  onSubmit(): void {
    if (this.forgotPasswordForm.valid) {
      const email = this.forgotPasswordForm.get('email')?.value;
      console.log('Reset password email sent to:', email);
      
      // Ici vous pouvez appeler votre service pour envoyer l'email de réinitialisation
      // this.authService.forgotPassword(email).subscribe(...)
      //   this.authService.forgotPassword(email).subscribe({
      //   next: () => {
      //     alert('Un email de réinitialisation a été envoyé à votre adresse.');
      //     this.router.navigate(['/login']);
      //   },
      //   error: (err) => {
      //     console.error('Erreur lors de l’envoi de l’email :', err);
      //     alert("Erreur lors de l’envoi de l'email. Veuillez réessayer.");
      //   }
      // });
//       this.authService.forgotPassword(email).subscribe({
//   next: () => {
//     alert('Un email de réinitialisation a été envoyé à votre adresse.');
//     this.router.navigate(['/login']);
//   },
//   error: (err) => {
//     console.error('Erreur lors de l’envoi de l’email :', err);
//     alert("Erreur : le serveur ne répond pas pour l’instant.");
//   }
// });

      // Optionnel : afficher un message de confirmation ou rediriger
      alert('Un email de réinitialisation a été envoyé à votre adresse.');
      
    } else {
      this.forgotPasswordForm.markAllAsTouched();
    }
  }

  goBack(): void {
    this.router.navigate(['/login']);
  }

  goToLogin(): void {
    this.router.navigate(['/login']);
  }

}
