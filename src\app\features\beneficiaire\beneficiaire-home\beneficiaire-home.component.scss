.profile-container {
  max-width: 900px;
  margin: 1rem auto 1rem auto;
  padding: 2rem;
  background: #f4f6f8;
  border-radius: 12px;
  box-shadow: 0 0 10px rgba(0, 0, 0, 0.05);
  font-family: 'Roboto', sans-serif;
  margin-top: 7rem;

  .title {
    font-size: 1.8rem;
    margin-bottom: 1.5rem;
    color: #3f51b5;
  }

  .profile-card {
    display: flex;
    flex-direction: row;
    gap: 2rem;
    background: white;
    border-radius: 12px;
    padding: 1.5rem;

    .avatar-section {
      display: flex;
      flex-direction: column;
      align-items: center;

      .avatar {
        width: 100px;
        height: 100px;
        border-radius: 50%;
        border: 2px solid #ccc;
        margin-bottom: 0.5rem;
      }

      .edit-btn {
        background: #673ab7;
        color: white;
        border: none;
        padding: 0.5rem 1rem;
        border-radius: 8px;
        cursor: pointer;
      }
    }

    .info-section {
      flex: 1;
      p {
        margin: 0.3rem 0;
      }
    }
  }

  .documents-section, .admin-actions {
    margin-top: 2rem;
    background: #fff;
    border-radius: 12px;
    padding: 1rem;

    h3 {
      color: #3f51b5;
    }

    ul {
      list-style: none;
      padding: 0;

      li {
        margin-bottom: 0.8rem;
        display: flex;
        justify-content: space-between;

        button {
          background: #2196f3;
          color: white;
          border: none;
          padding: 0.4rem 0.8rem;
          border-radius: 6px;
          cursor: pointer;
        }
      }
    }
  }

  .edit-profile {
    margin-top: 2rem;
    background: #ff9800;
    color: white;
    border: none;
    padding: 0.7rem 1.4rem;
    border-radius: 10px;
    cursor: pointer;
  }
}
