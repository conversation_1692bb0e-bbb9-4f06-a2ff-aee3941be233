/* You can add global styles to this file, and also import other style files */

html, body {
  height: 100%;
  margin: 0;
  padding: 0;
  scroll-behavior: smooth;
}
.app-container {
  min-height: auto;
  display: flex;
  flex-direction: column;
  margin-bottom: 0;
  padding-bottom: 0;
}

router-outlet {
  flex: 1; // <PERSON><PERSON>e le footer en bas
}

// Style global pour minimiser l'espace avec le footer
.page-content {
  margin-bottom: 1rem;
  padding-bottom: 0;
}

body { margin: 0; font-family: Robot<PERSON>, "Helvetica Neue", sans-serif; }

// Styles pour le dialogue
.custom-dialog-container .mat-dialog-container {
  padding: 0;
  overflow: hidden;
  border-radius: 10px;
}

// Animation pour l'ouverture du dialogue
@keyframes fadeIn {
  from { opacity: 0; transform: translateY(-20px); }
  to { opacity: 1; transform: translateY(0); }
}

.cdk-overlay-container .cdk-overlay-pane {
  animation: fadeIn 0.3s ease-out;
}
