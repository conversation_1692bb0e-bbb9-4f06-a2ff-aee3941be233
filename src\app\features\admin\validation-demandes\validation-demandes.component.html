    <div class="container-fluid p-4">
      <div class="row">
        <div class="col-12">
          <h2 class="mb-4">
            <mat-icon class="me-2">assignment_turned_in</mat-icon>
            Validation des Demandes d'appareillage
          </h2>
        </div>
      </div>

      <div class="row">
        <!-- Statistiques -->
        <div class="col-md-3 mb-3">
          <mat-card class="stats-card text-center">
            <mat-card-content>
              <mat-icon class="stats-icon text-warning">pending</mat-icon>
              <h3>{{ demandesEnAttente.length }}</h3>
              <p>En attente</p>
            </mat-card-content>
          </mat-card>
        </div>
        <div class="col-md-3 mb-3">
          <mat-card class="stats-card text-center">
            <mat-card-content>
              <mat-icon class="stats-icon text-success">check_circle</mat-icon>
              <h3>{{ demandesApprouvees.length }}</h3>
              <p>Approuvées</p>
            </mat-card-content>
          </mat-card>
        </div>
        <div class="col-md-3 mb-3">
          <mat-card class="stats-card text-center">
            <mat-card-content>
              <mat-icon class="stats-icon text-danger">cancel</mat-icon>
              <h3>{{ demandesRejetees.length }}</h3>
              <p>Rejetées</p>
            </mat-card-content>
          </mat-card>
        </div>
        <div class="col-md-3 mb-3">
          <mat-card class="stats-card text-center">
            <mat-card-content>
              <mat-icon class="stats-icon text-info">assignment</mat-icon>
              <h3>{{ demandes.length }}</h3>
              <p>Total</p>
            </mat-card-content>
          </mat-card>
        </div>
      </div>

      <!-- Liste des demandes -->
      <div class="row">
        <div class="col-12">
          <mat-card>
            <mat-card-header>
              <mat-card-title>Demandes à Valider</mat-card-title>
            </mat-card-header>
            <mat-card-content>
              <div class="demandes-list">
                <div *ngFor="let demande of demandes" class="demande-item mb-4">
                  <mat-card class="demande-card" [ngClass]="getStatusClass(demande.statusValidation)">
                    <mat-card-header>
                      <div class="d-flex justify-content-between align-items-center w-100">
                        <div>
                          <mat-card-title class="h5">
                            {{ demande.prenom }} {{ demande.nom }}
                          </mat-card-title>
                          <mat-card-subtitle>
                            <mat-icon class="me-1">email</mat-icon>
                            {{ demande.email }}
                          </mat-card-subtitle>
                        </div>
                        <div class="status-badge">
                          <mat-chip [ngClass]="getStatusChipClass(demande.statusValidation)">
                            <mat-icon class="me-1">{{ getStatusIcon(demande.statusValidation) }}</mat-icon>
                            {{ getStatusText(demande.statusValidation) }}
                          </mat-chip>
                        </div>
                      </div>
                    </mat-card-header>
                    
                    <mat-card-content>
                      <div class="row">
                        <div class="col-md-6">
                          <div class="info-section">
                            <h6><mat-icon class="me-1">person</mat-icon>Informations Personnelles</h6>
                            <div class="info-item">
                              <strong>Sexe:</strong> {{ demande.sexe }}
                            </div>
                            <div class="info-item">
                              <strong>Date de naissance:</strong> {{ demande.dateNaissance }}
                            </div>
                            <div class="info-item">
                              <strong>Adresse:</strong> {{ demande.adresse }}
                            </div>
                          </div>
                        </div>
                        
                        <div class="col-md-6">
                          <div class="info-section">
                            <h6><mat-icon class="me-1">medical_services</mat-icon>Informations Médicales</h6>
                            <div class="info-item">
                              <strong>Statut:</strong> {{ demande.statut }}
                            </div>
                            <div class="info-item">
                              <strong>Couverture:</strong> {{ demande.couverture }}
                            </div>
                            <div class="info-item">
                              <strong>Type:</strong> {{ demande.type }}
                            </div>
                          </div>
                        </div>
                      </div>

                      <mat-divider class="my-3"></mat-divider>

                      <div class="documents-section">
                        <h6><mat-icon class="me-1">folder</mat-icon>Documents Téléversés</h6>
                        <div class="row">
                          <div class="col-md-3 col-sm-6 mb-2">
                            <button mat-stroked-button color="primary" class="w-100" (click)="voirDocument(demande.documents.cin)">
                              <mat-icon class="me-1">credit_card</mat-icon>
                              CIN
                            </button>
                          </div>
                          <div class="col-md-3 col-sm-6 mb-2">
                            <button mat-stroked-button color="primary" class="w-100" (click)="voirDocument(demande.documents.amo)">
                              <mat-icon class="me-1">assignment</mat-icon>
                              AMO
                            </button>
                          </div>
                          <div class="col-md-3 col-sm-6 mb-2">
                            <button mat-stroked-button color="primary" class="w-100" (click)="voirDocument(demande.documents.certificatMedical)">
                              <mat-icon class="me-1">local_hospital</mat-icon>
                              Certificat Médical
                            </button>
                          </div>
                          <div class="col-md-3 col-sm-6 mb-2" *ngIf="demande.documents.certificatHandicape">
                            <button mat-stroked-button color="primary" class="w-100" (click)="voirDocument(demande.documents.certificatHandicape!)">
                              <mat-icon class="me-1">accessible</mat-icon>
                              Certificat Handicap
                            </button>
                          </div>
                        </div>
                      </div>

                      <div class="date-info mt-3">
                        <small class="text-muted">
                          <mat-icon class="me-1" style="font-size: 16px;">schedule</mat-icon>
                          Soumise le {{ demande.dateSubmission | date:'dd/MM/yyyy à HH:mm' }}
                        </small>
                      </div>
                    </mat-card-content>

                    <mat-card-actions class="d-flex justify-content-end" *ngIf="demande.statusValidation === 'en_attente'">
                      <button mat-raised-button color="warn" (click)="rejeterDemande(demande)" class="me-2">
                        <mat-icon class="me-1">close</mat-icon>
                        Rejeter
                      </button>
                      <button mat-raised-button color="primary" (click)="approuverDemande(demande)">
                        <mat-icon class="me-1">check</mat-icon>
                        Approuver
                      </button>
                    </mat-card-actions>
                  </mat-card>
                </div>
              </div>

              <div *ngIf="demandes.length === 0" class="text-center py-5">
                <mat-icon style="font-size: 48px; color: #ccc;">inbox</mat-icon>
                <p class="mt-3 text-muted">Aucune demande à valider pour le moment</p>
              </div>
            </mat-card-content>
          </mat-card>
        </div>
      </div>
    </div>
