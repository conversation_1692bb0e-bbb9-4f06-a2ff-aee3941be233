<div class="back-title-container">
  <span class="back-arrow" (click)="onBack()">
    ←
  </span>
  <h2 class="page-title">Suivi des demandes</h2>
</div>
<div class="table-wrapper">
<div class="table-header row g-0"> <!-- g-0 supprime les gutters -->
  <div class="col-2">N° Demande</div>
  <div class="col-6 text-center px-0">Appareil demandé</div> <!-- px-0 supprime le padding horizontal -->
  <div class="col-4 text-end pe-3">Statut</div> <!-- pe-3 = padding-right modéré -->
</div>
<div class="table-row row g-0" *ngFor="let demande of demandes">
  <div class="col-2">{{ demande.id }}</div>
  <div class="col-6 text-center px-0">{{ demande.materiel }}</div>
  <div class="col-4 text-end pe-3">
    <span class="status-pill" [ngClass]="'status-' + demande.statut.toLowerCase()">
      {{ demande.statutLibelle }}
    </span>
  </div>
</div>
</div>
