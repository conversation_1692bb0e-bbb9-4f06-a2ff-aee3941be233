import { Component } from '@angular/core';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatIconModule } from '@angular/material/icon';
import { MatButtonModule } from '@angular/material/button';
import { FormsModule } from '@angular/forms';
import { CommonModule } from '@angular/common';
import { Router } from '@angular/router';
import { MatCardModule } from '@angular/material/card';

interface Service {
  id: number;
  title: string;
  image: string;
}

export interface Evenement {
  id: number;
  titre: string;
  beneficiaires: number;
  image: string;
  date?: Date;
  lieu?: string;
  description?: string;
}

interface SearchSuggestion {
  text: string;
  category: string;
  icon: string;
  section: string;
}

@Component({
  selector: 'app-home',
  imports: [ MatFormFieldModule, MatInputModule, MatCardModule,
     MatIconModule, MatButtonModule,
      FormsModule, CommonModule ],
  templateUrl: './home.component.html',
  styleUrl: './home.component.scss'
})
export class HomeComponent {

  searchQuery: string = '';
  searchSuggestions: SearchSuggestion[] = [];

  // Dictionnaire de mots-clés pour la recherche intelligente
  private searchKeywords = {
    services: [
      'lunettes', 'appareil', 'auditif', 'canne', 'pied', 'fauteuil', 'roulant',
      'prothèse', 'protheses', 'équipement', 'equipement', 'médical', 'medical',
      'service', 'services', 'aide', 'assistance', 'matériel', 'materiel'
    ],
    evenements: [
      'dépistage', 'depistage', 'diabète', 'diabete', 'vaccination', 'vaccin',
      'don', 'sang', 'consultation', 'gratuit', 'gratuite', 'médicament', 'medicament',
      'soins', 'dentaire', 'santé', 'sante', 'mentale', 'rééducation', 'reeducation',
      'handicap', 'handicapé', 'handicape', 'événement', 'evenement', 'campagne',
      'distribution', 'sensibilisation'
    ],
    contact: [
      'contact', 'téléphone', 'telephone', 'email', 'adresse', 'localisation',
      'appeler', 'contacter', 'joindre', 'information', 'renseignement'
    ]
  };

  onSearch() {
    if (this.searchQuery.trim()) {
      console.log('Recherche pour:', this.searchQuery);
      this.performIntelligentSearch(this.searchQuery.trim().toLowerCase());
      this.searchSuggestions = []; // Cacher les suggestions après la recherche
    }
  }

  onSearchInput(event: any): void {
    const query = event.target.value.toLowerCase().trim();
    if (query.length > 1) {
      this.generateSuggestions(query);
    } else {
      this.searchSuggestions = [];
    }
  }

  selectSuggestion(suggestion: SearchSuggestion): void {
    this.searchQuery = suggestion.text;
    this.searchSuggestions = [];
    this.scrollToSection(suggestion.section);
  }

  private generateSuggestions(query: string): void {
    const suggestions: SearchSuggestion[] = [];

    // Suggestions pour les services
    this.services.forEach(service => {
      if (service.title.toLowerCase().includes(query)) {
        suggestions.push({
          text: service.title,
          category: 'Service',
          icon: 'medical_services',
          section: 'services-section'
        });
      }
    });

    // Suggestions pour les événements
    this.evenements.forEach(event => {
      if (event.titre.toLowerCase().includes(query) ||
          (event.description && event.description.toLowerCase().includes(query))) {
        suggestions.push({
          text: event.titre,
          category: 'Événement',
          icon: 'event',
          section: 'evenements-container'
        });
      }
    });

    // Suggestions génériques basées sur les mots-clés
    if (this.searchKeywords.services.some(keyword => keyword.includes(query))) {
      suggestions.push({
        text: 'Voir tous les services',
        category: 'Section',
        icon: 'medical_services',
        section: 'services-section'
      });
    }

    if (this.searchKeywords.evenements.some(keyword => keyword.includes(query))) {
      suggestions.push({
        text: 'Voir tous les événements',
        category: 'Section',
        icon: 'event',
        section: 'evenements-container'
      });
    }

    if (this.searchKeywords.contact.some(keyword => keyword.includes(query))) {
      suggestions.push({
        text: 'Informations de contact',
        category: 'Contact',
        icon: 'contact_phone',
        section: 'footer'
      });
    }

    this.searchSuggestions = suggestions.slice(0, 5); // Limiter à 5 suggestions
  }

  private performIntelligentSearch(query: string): void {
    const section = this.detectSearchSection(query);

    switch (section) {
      case 'services':
        this.scrollToSection('services-section');
        break;
      case 'evenements':
        this.scrollToSection('evenements-container');
        break;
      case 'contact':
        this.scrollToSection('footer');
        break;
      default:
        // Si aucune section spécifique n'est détectée, chercher dans les contenus
        this.searchInContent(query);
        break;
    }
  }

  private detectSearchSection(query: string): string | null {
    const words = query.split(' ');

    for (const word of words) {
      // Vérifier les services
      if (this.searchKeywords.services.some(keyword =>
          keyword.includes(word) || word.includes(keyword))) {
        return 'services';
      }

      // Vérifier les événements
      if (this.searchKeywords.evenements.some(keyword =>
          keyword.includes(word) || word.includes(keyword))) {
        return 'evenements';
      }

      // Vérifier le contact
      if (this.searchKeywords.contact.some(keyword =>
          keyword.includes(word) || word.includes(keyword))) {
        return 'contact';
      }
    }

    return null;
  }

  private searchInContent(query: string): void {
    // Rechercher dans les titres et descriptions des services
    const serviceMatch = this.services.find(service =>
      service.title.toLowerCase().includes(query)
    );

    if (serviceMatch) {
      this.scrollToSection('services-section');
      return;
    }

    // Rechercher dans les événements
    const eventMatch = this.evenements.find(event =>
      event.titre.toLowerCase().includes(query) ||
      (event.description && event.description.toLowerCase().includes(query))
    );

    if (eventMatch) {
      this.scrollToSection('evenements-container');
      return;
    }

    // Si rien n'est trouvé, afficher un message ou rester en haut
    console.log('Aucun résultat trouvé pour:', query);
  }

  private scrollToSection(sectionId: string): void {
    const element = document.getElementById(sectionId);
    if (element) {
      element.scrollIntoView({
        behavior: 'smooth',
        block: 'start'
      });
    }
  }

  // Index séparés pour chaque carousel
  servicesCurrentIndex = 0;
  eventsCurrentIndex = 0;
  servicesPerPage = 3;

  services: Service[] = [
    {
      id: 1,
      title: 'Lunettes',
      image: 'assets/images/lunettes.jpg'
    },
    {
      id: 2,
      title: 'Appareils auditives',
      image: 'assets/images/appareils-auditifs.jpg'
    },
    {
      id: 3,
      title: 'Canne à pied',
      image: 'assets/images/canne-pied.jpg'
    },
    {
      id: 4,
      title: 'Fauteuil roulant',
      image: 'assets/images/fauteuil-roulant.jpg'
    },
    {
      id: 5,
      title: 'Prothèses',
      image: 'assets/images/protheses.jpg'
    },
    {
      id: 6,
      title: 'Équipements médicaux',
      image: 'assets/images/canne-a-pied.jpg'
    }
  ];

  constructor(private router: Router) {
    this.updateCardsPerView();
    this.updateTranslateX();
  }

  get totalPages(): number {
    return Math.max(0, this.services.length - this.servicesPerPage + 1);
  }

  getCurrentServices(): Service[] {
    return this.services.slice(this.servicesCurrentIndex, this.servicesCurrentIndex + this.servicesPerPage);
  }

  nextPage(): void {
    if (this.servicesCurrentIndex < this.services.length - this.servicesPerPage) {
      this.servicesCurrentIndex++;
    }
  }

  previousPage(): void {
    if (this.servicesCurrentIndex > 0) {
      this.servicesCurrentIndex--;
    }
  }

  goToPage(pageIndex: number): void {
    this.servicesCurrentIndex = pageIndex;
  }

  getPageIndicators(): number[] {
    return Array.from({ length: this.totalPages }, (_, i) => i);
  }

  makeRequest(): void {
    this.router.navigate(['/beneficiaire/demandes']);
  }

  trackByServiceId(index: number, service: Service): number {
    return service.id;
  }

  evenements: Evenement[] = [
    {
      id: 1,
      titre: 'Dépistage du diabète',
      beneficiaires: 80,
      image: 'assets/images/dipestage.jpg',
      date: new Date('2023-12-15'),
      lieu: 'Hôpital Central',
      description: 'Campagne de dépistage gratuit du diabète pour tous les citoyens de plus de 40 ans.'
    },
    {
      id: 2,
      titre: 'Campagne de vaccination gratuite',
      beneficiaires: 200,
      image: 'assets/images/vaccinnation-rougeole.jpg',
      date: new Date('2023-11-20'),
      lieu: 'Centre de Santé Communautaire',
      description: 'Vaccination gratuite contre la rougeole pour les enfants de moins de 5 ans.'
    },
    {
      id: 3,
      titre: 'Dons de sang organisés',
      beneficiaires: 150,
      image: 'assets/images/donnate_sang.jpg',
      date: new Date('2023-10-10'),
      lieu: 'Stade Municipal',
      description: 'Collecte de dons de sang pour les patients en nécessité.'
    },
    {
      id: 4,
      titre: 'Consultations médicales gratuites',
      beneficiaires: 100,
      image: 'assets/images/consultation.jfif',
      date: new Date('2023-09-05'),
      lieu: 'Clinique du Centre',
      description: 'Consultations médicales gratuites pour les personnes âgées de 65 ans et plus.'
    },
    {
      id: 5,
      titre: 'Distribution de médicaments essentiels',
      beneficiaires: 130,
      image: 'assets/images/pharmacie.jpg',
      date: new Date('2023-08-15'),
      lieu: 'Pharmacie Centrale',
      description: 'Distribution gratuite de médicaments essentiels pour les personnes à faible revenu.'
    },
    {
      id: 6,
      titre: 'Soins dentaires pour enfants',
      beneficiaires: 70,
      image: 'assets/images/dentaires.jfif',
      date: new Date('2023-07-25'),
      lieu: 'Clinique Dentaire',
      description: 'Soins dentaires gratuits pour les enfants de moins de 12 ans.'
    },
    {
      id: 7,
      titre: 'Sensibilisation à la santé mentale',
      beneficiaires: 90,
      image: 'assets/images/sante_mentale.jpg',
      date: new Date('2023-06-30'),
      lieu: 'Centre de Santé Mentale',
      description: 'Séances de sensibilisation à la santé mentale pour les adolescents.'
    },
    {
      id: 8,
      titre: 'Rééducation pour personnes handicapées',
      beneficiaires: 65,
      image: 'assets/images/handicape.jpg',
      date: new Date('2023-05-15'),
      lieu: 'Centre de Rééducation',
      description: 'Programme de rééducation pour les personnes handicapées physiques.'
    }
  ];

  cardsPerView = 3; // Affiche 3 cartes par défaut
  hoveredCardIndex: number | null = null;
  translateX = 0;
  cardWidth = 320; // Ajusté pour 3 cartes
  Math = Math;

  // Comportement du carousel :
  // - Affiche 3 cartes initialement
  // - Défile une carte à la fois avec les flèches
  // - Les indicateurs correspondent à chaque position possible

  get dots(): number[] {
    // Calculer le nombre d'indicateurs basé sur le défilement une carte à la fois
    const maxIndex = Math.max(0, this.evenements.length - this.cardsPerView);
    const totalDots = maxIndex + 1;
    return Array(totalDots).fill(0).map((_, i) => i);
  }

  ngOnInit() {
    this.updateResponsiveSettings();
    window.addEventListener('resize', () => this.updateResponsiveSettings());

    // Fermer les suggestions quand on clique ailleurs
    document.addEventListener('click', (event) => {
      const searchContainer = document.querySelector('.search-container');
      if (searchContainer && !searchContainer.contains(event.target as Node)) {
        this.searchSuggestions = [];
      }
    });
  }

  updateResponsiveSettings() {
    const oldCardsPerView = this.cardsPerView;
    this.updateCardsPerView();

    // Ajuster l'index si le nombre de cartes visibles a changé
    if (oldCardsPerView !== this.cardsPerView) {
      const maxIndex = Math.max(0, this.evenements.length - this.cardsPerView);
      if (this.eventsCurrentIndex > maxIndex) {
        this.eventsCurrentIndex = maxIndex;
      }
    }

    this.updateTranslateX();
  }

  updateCardsPerView() {
    const width = window.innerWidth;
    if (width <= 576) {
      this.cardsPerView = 1;
      this.cardWidth = 280;
    } else if (width <= 768) {
      this.cardsPerView = 2;
      this.cardWidth = 250;
    } else if (width <= 1200) {
      this.cardsPerView = 3;
      this.cardWidth = 300; // Ajusté pour 3 cartes
    } else {
      this.cardsPerView = 3; // Toujours 3 cartes sur desktop
      this.cardWidth = 320; // Ajusté pour 3 cartes
    }
  }

  updateTranslateX() {
    const gap = 24; // Correspond au gap de 1.5rem dans le CSS
    this.translateX = -this.eventsCurrentIndex * (this.cardWidth + gap);
  }

  nextSlide() {
    // Avancer d'une carte à la fois
    const maxIndex = Math.max(0, this.evenements.length - this.cardsPerView);
    if (this.eventsCurrentIndex < maxIndex) {
      this.eventsCurrentIndex++;
      this.updateTranslateX();
    }
  }

  previousSlide() {
    // Reculer d'une carte à la fois
    if (this.eventsCurrentIndex > 0) {
      this.eventsCurrentIndex--;
      this.updateTranslateX();
    }
  }

  goToSlide(slideIndex: number) {
    // Aller directement à l'index spécifié (une carte à la fois)
    const maxIndex = Math.max(0, this.evenements.length - this.cardsPerView);
    this.eventsCurrentIndex = Math.min(slideIndex, maxIndex);
    this.updateTranslateX();
  }

  onCardHover(index: number, isHovered: boolean) {
    this.hoveredCardIndex = isHovered ? index : null;
  }

  voirPlus() {
    this.router.navigate(['/admin/gestion-evenements']);
  }

  participerEvenement() {
    this.router.navigate(['/beneficiaire/participation']);
  }

  isLastSlide(): boolean {
    const maxPossibleIndex = Math.max(0, this.evenements.length - this.cardsPerView);
    return this.eventsCurrentIndex >= maxPossibleIndex;
  }
}
