version: '3.8'

services:
  mysql:
    image: mysql:8.0
    container_name: delegation-db
    environment:
      MYSQL_ROOT_PASSWORD: root
      MYSQL_DATABASE: delegation_sante_db
    ports:
      - "3306:3306"
    volumes:
      - mysql-data:/var/lib/mysql
    networks:
      - delegation-network

  backend:
    build: ./backend
    container_name: delegation-backend
    depends_on:
      - mysql
    environment:
      SPRING_DATASOURCE_URL: *************************************************************************
      SPRING_DATASOURCE_USERNAME: root
      SPRING_DATASOURCE_PASSWORD: root
    ports:
      - "8080:8080"
    networks:
      - delegation-network

  frontend:
    build: .
    container_name: delegation-frontend
    ports:
      - "4200:80"
    depends_on:
      - backend
    networks:
      - delegation-network

networks:
  delegation-network:
    driver: bridge

volumes:
  mysql-data: 