export interface User {
  id: string;
  email: string;
  nom: string;
  prenom: string;
  role: 'ADMIN' | 'BENEFICIAIRE';
  password?: string;
}

export class UserModel implements User {
  constructor(
    public id: string,
    public email: string,
    public nom: string,
    public prenom: string,
    public role: 'ADMIN' | 'BENEFICIAIRE',
    public password?: string
  ) {}

  isAdmin(): boolean {
    return this.role === 'ADMIN';
  }

  isBeneficiaire(): boolean {
    return this.role === 'BENEFICIAIRE';
  }

  getFullName(): string {
    return `${this.prenom} ${this.nom}`;
  }
}
