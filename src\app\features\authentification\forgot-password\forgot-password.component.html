<!-- <p>forgot-password works!</p> -->
 <div class="password-change-container">
  <div class="password-change-card">
    <div class="header">
      <button class="back-button" (click)="goBack()">
        <span class="material-icons">arrow_back</span>
      </button>
      <h1>Changer mot de passe</h1>
    </div>

    <form [formGroup]="passwordForm" (ngSubmit)="onSubmit()">

      <div class="form-field">
        <label for="newPassword">Nouveau mot de passe</label>
        <div class="password-input-container">
          <input 
            [type]="hideNewPassword ? 'password' : 'text'"
            id="newPassword" 
            formControlName="newPassword">
          <button 
            type="button" 
            class="toggle-visibility" 
            (click)="togglePasswordVisibility('new')">
            <span class="material-icons">
              {{ hideNewPassword ? 'visibility' : 'visibility_off' }}
            </span>
          </button>
        </div>
        <div class="error-message" *ngIf="passwordForm.get('newPassword')?.touched && passwordForm.get('newPassword')?.errors?.['required']">
          Le nouveau mot de passe est requis
        </div>
        <div class="error-message" *ngIf="passwordForm.get('newPassword')?.touched && passwordForm.get('newPassword')?.errors?.['minlength']">
          Le mot de passe doit contenir au moins 8 caractères
        </div>
      </div>

      <div class="form-field">
        <label for="confirmPassword">Confirmer nouveau mot de passe</label>
        <div class="password-input-container">
          <input 
            [type]="hideConfirmPassword ? 'password' : 'text'"
            id="confirmPassword" 
            formControlName="confirmPassword">
          <button 
            type="button" 
            class="toggle-visibility" 
            (click)="togglePasswordVisibility('confirm')">
            <span class="material-icons">
              {{ hideConfirmPassword ? 'visibility' : 'visibility_off' }}
            </span>
          </button>
        </div>
        <div class="error-message" *ngIf="passwordForm.get('confirmPassword')?.touched && passwordForm.get('confirmPassword')?.errors?.['required']">
          La confirmation du mot de passe est requise
        </div>
        <div class="error-message" *ngIf="passwordForm.get('confirmPassword')?.touched && passwordForm.get('confirmPassword')?.errors?.['passwordMismatch']">
          Les mots de passe ne correspondent pas
        </div>
      </div>

      <button type="submit" class="confirm-button" [disabled]="passwordForm.invalid">
        Confirmer
      </button>
    </form>
  </div>
</div>
