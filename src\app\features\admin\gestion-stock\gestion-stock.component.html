<div class="container-fluid">
  <!-- Header avec titre et bouton retour -->
  <div class="back-title-container">
  <span class="back-arrow" (click)="onBack()">
    ←
  </span>
  <h2 class="page-title">Gestion des appareils</h2>
</div>

  <!-- Section Ajouter un appareil -->
  <div class="row mb-5">
    <div class="col-12">
      <mat-card class="shadow-sm">
        <mat-card-header>
          <mat-card-title>Ajouter un appareil à disposition</mat-card-title>
        </mat-card-header>
        
        <mat-card-content>
          <form [formGroup]="appareilForm" (ngSubmit)="creerAppareil()">
            <div class="row g-3">
              <!-- Type d'appareil -->
              <div class="col-md-6">
                <mat-form-field appearance="outline" class="w-100">
                  <mat-label>Type d'appareil</mat-label>
                  <input matInput formControlName="typeAppareil" placeholder="Ex: Lunettes, Appareils auditifs...">
                  <mat-error *ngIf="appareilForm.get('typeAppareil')?.hasError('required')">
                    Le type d'appareil est requis
                  </mat-error>
                </mat-form-field>
              </div>

              <!-- Description -->
              <div class="col-md-6">
                <mat-form-field appearance="outline" class="w-100">
                  <mat-label>Description</mat-label>
                  <input matInput formControlName="description" placeholder="Description de l'appareil">
                  <mat-error *ngIf="appareilForm.get('description')?.hasError('required')">
                    La description est requise
                  </mat-error>
                </mat-form-field>
              </div>

              <!-- Quantité disponible -->
              <div class="col-md-6">
                <mat-form-field appearance="outline" class="w-100">
                  <mat-label>Quantité disponible</mat-label>
                  <input matInput type="number" formControlName="quantiteDisponible" min="1">
                  <mat-error *ngIf="appareilForm.get('quantiteDisponible')?.hasError('required')">
                    La quantité est requise
                  </mat-error>
                  <mat-error *ngIf="appareilForm.get('quantiteDisponible')?.hasError('min')">
                    La quantité doit être supérieure à 0
                  </mat-error>
                </mat-form-field>
              </div>

              <!-- Société fournissante -->
              <div class="col-md-6">
                <mat-form-field appearance="outline" class="w-100">
                  <mat-label>Société fournissante</mat-label>
                  <input matInput formControlName="societeFournissante" placeholder="Nom de la société">
                  <mat-error *ngIf="appareilForm.get('societeFournissante')?.hasError('required')">
                    La société fournissante est requise
                  </mat-error>
                </mat-form-field>
              </div>

              <!-- Photo -->
              <div class="col-12">
                <div class="mb-3">
                  <label class="form-label">Photo</label>
                  <div class="d-flex align-items-center">
                    <input type="file" 
                           class="form-control me-3" 
                           accept="image/*" 
                           (change)="onFileSelected($event)"
                           style="max-width: 300px;">
                    <mat-icon class="text-muted">add_photo_alternate</mat-icon>
                  </div>
                  <small class="text-muted">Formats acceptés: JPG, PNG, GIF (max 5MB)</small>
                </div>
              </div>
            </div>

            <div class="row mt-4">
              <div class="col-12 text-center">
                <button mat-raised-button 
                        color="primary" 
                        type="submit" 
                        class="px-4 py-2"
                        [disabled]="!appareilForm.valid">
                  <mat-icon class="me-2">add</mat-icon>
                  Créer
                </button>
              </div>
            </div>
          </form>
        </mat-card-content>
      </mat-card>
    </div>
  </div>

  <!-- Section Événements existants -->
  <div class="row">
    <div class="col-12">
      <mat-card class="shadow-sm">
        <mat-card-header>
          <mat-card-title>Événements existants</mat-card-title>
        </mat-card-header>
        
        <mat-card-content>
          <!-- Tableau des appareils -->
          <div *ngIf="appareils.length > 0; else noAppareils" class="table-responsive">
            <table mat-table [dataSource]="appareils" class="mat-elevation-z2 w-100">
              
              <!-- Colonne ID -->
              <ng-container matColumnDef="id">
                <th mat-header-cell *matHeaderCellDef>Id</th>
                <td mat-cell *matCellDef="let appareil">{{ appareil.id }}</td>
              </ng-container>

              <!-- Colonne Type d'appareil -->
              <ng-container matColumnDef="typeAppareil">
                <th mat-header-cell *matHeaderCellDef>Type d'appareil</th>
                <td mat-cell *matCellDef="let appareil">{{ appareil.typeAppareil }}</td>
              </ng-container>

              <!-- Colonne Description -->
              <ng-container matColumnDef="description">
                <th mat-header-cell *matHeaderCellDef>Description</th>
                <td mat-cell *matCellDef="let appareil">{{ appareil.description }}</td>
              </ng-container>

              <!-- Colonne Quantité disponible -->
              <ng-container matColumnDef="quantiteDisponible">
                <th mat-header-cell *matHeaderCellDef>Quantité disponible</th>
                <td mat-cell *matCellDef="let appareil">
                  <span class="badge bg-success">{{ appareil.quantiteDisponible }}</span>
                </td>
              </ng-container>

              <!-- Colonne Société fournissante -->
              <ng-container matColumnDef="societeFournissante">
                <th mat-header-cell *matHeaderCellDef>Société fournissante</th>
                <td mat-cell *matCellDef="let appareil">{{ appareil.societeFournissante }}</td>
              </ng-container>

              <!-- Colonne Photo -->
              <ng-container matColumnDef="photo">
                <th mat-header-cell *matHeaderCellDef>Photo</th>
                <td mat-cell *matCellDef="let appareil">
                  <div class="photo-container">
                    <img *ngIf="appareil.photo" 
                         [src]="appareil.photo" 
                         alt="Photo de l'appareil" 
                         class="img-thumbnail"
                         style="width: 50px; height: 50px; object-fit: cover;">
                    <mat-icon *ngIf="!appareil.photo" class="text-muted">image_not_supported</mat-icon>
                  </div>
                </td>
              </ng-container>

              <!-- Colonne Actions -->
              <ng-container matColumnDef="actions">
                <th mat-header-cell *matHeaderCellDef>Actions</th>
                <td mat-cell *matCellDef="let appareil">
                  <div class="d-flex gap-1">
                    <button mat-icon-button 
                            color="primary" 
                            (click)="modifier(appareil)"
                            matTooltip="Modifier"
                            aria-label="Modifier">
                      <mat-icon>edit</mat-icon>
                    </button>
                    <button mat-icon-button 
                            color="warn" 
                            (click)="supprimer(appareil.id)"
                            matTooltip="Supprimer"
                            aria-label="Supprimer">
                      <mat-icon>delete</mat-icon>
                    </button>
                    <button mat-icon-button 
                            color="accent" 
                            (click)="voir(appareil)"
                            matTooltip="Voir"
                            aria-label="Voir">
                      <mat-icon>visibility</mat-icon>
                    </button>
                  </div>
                </td>
              </ng-container>

              <tr mat-header-row *matHeaderRowDef="displayedColumns"></tr>
              <tr mat-row *matRowDef="let row; columns: displayedColumns;"></tr>
            </table>
          </div>

          <!-- Message si aucun appareil -->
          <ng-template #noAppareils>
            <div class="text-center py-5">
              <mat-icon class="text-muted mb-3" style="font-size: 48px; height: 48px; width: 48px;">
                device_unknown
              </mat-icon>
              <h5 class="text-muted">Aucun appareil</h5>
              <p class="text-muted">Aucun appareil n'a été ajouté pour le moment.</p>
            </div>
          </ng-template>
        </mat-card-content>
      </mat-card>
    </div>
  </div>
</div>