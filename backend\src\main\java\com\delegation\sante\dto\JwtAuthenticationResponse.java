package com.delegation.sante.dto;

import lombok.Data;

@Data
public class JwtAuthenticationResponse {
    private String token;
    private String tokenType = "Bearer";
    private Long id;
    private String email;
    private String role;

    public JwtAuthenticationResponse(String token, Long id, String email, String role) {
        this.token = token;
        this.id = id;
        this.email = email;
        this.role = role;
    }
} 