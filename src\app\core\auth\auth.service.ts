import { Injectable, signal, computed } from '@angular/core';
import { Router } from '@angular/router';
import { jwtDecode } from 'jwt-decode';
import { Observable, throwError } from 'rxjs';
import { catchError, tap } from 'rxjs/operators';
import { ApiService } from '../api/api.service';

interface DecodedToken {
  sub: string;
  email: string;
  role: 'BENEFICIAIRE' | 'ADMIN';
  exp: number;
  iat: number;
}

interface RegisterRequest {
  username: string;  // CIN
  email: string;
  password: string;
  firstName: string;
  lastName: string;
  phoneNumber: string;
}

interface LoginRequest {
  usernameOrEmail: string;
  password: string;
}

interface User {
  id: string;
  email: string;
  role: 'BENEFICIAIRE' | 'ADMIN';
}

@Injectable({
  providedIn: 'root'
})
export class AuthService {
  private readonly _isAuthenticated = signal<boolean>(false);
  private readonly _currentUser = signal<User | null>(null);
  private readonly _token = signal<string | null>(null);

  public isAuthenticated = computed(() => this._isAuthenticated());
  public currentUser = computed(() => this._currentUser());
  public token = computed(() => this._token());
  // private apiUrl = 'http://localhost:3000/api/auth';

  constructor(
    private apiService: ApiService,
    private router: Router
  ) {
    this.initializeAuthState();
  }

  private initializeAuthState(): void {
    const token = localStorage.getItem('auth_token');
    if (token && !this.isTokenExpired(token)) {
      try {
        const decoded = this.decodeToken(token);
        this._currentUser.set({
          id: decoded.sub,
          email: decoded.email,
          role: decoded.role
        });
        this._isAuthenticated.set(true);
        this._token.set(token);
      } catch (error) {
        this.clearAuth();
      }
    }
  }

  login(usernameOrEmail: string, password: string): Observable<{ token: string }> {
    const loginData: LoginRequest = {
      usernameOrEmail,
      password
    };

    return this.apiService.post<{ token: string }>('auth/login', loginData).pipe(
      tap(response => {
        const token = response.token;
        const decoded = this.decodeToken(token);

        this._currentUser.set({
          id: decoded.sub,
          email: decoded.email,
          role: decoded.role
        });
        this._isAuthenticated.set(true);
        this._token.set(token);

        localStorage.setItem('auth_token', token);
      }),
      catchError(error => {
        this.clearAuth();
        let errorMessage = 'Échec de la connexion';
        
        if (error.status === 401) {
          errorMessage = 'Identifiants incorrects';
        } else if (error.status === 403) {
          errorMessage = 'Compte non autorisé';
        } else if (error.status === 404) {
          errorMessage = 'Utilisateur non trouvé';
        } else if (error.error?.message) {
          errorMessage = error.error.message;
        }
        
        return throwError(() => new Error(errorMessage));
      })
    );
  }

  register(userData: RegisterRequest): Observable<any> {
    return this.apiService.post<any>('auth/register', userData).pipe(
      catchError(error => {
        let errorMessage = 'Échec d\'inscription';
        
        if (error.status === 400) {
          if (error.error?.message?.includes('username')) {
            errorMessage = 'Ce CIN est déjà utilisé';
          } else if (error.error?.message?.includes('email')) {
            errorMessage = 'Cet email est déjà utilisé';
          } else if (error.error?.message?.includes('phoneNumber')) {
            errorMessage = 'Ce numéro de téléphone est déjà utilisé';
          } else if (error.error?.message) {
            errorMessage = error.error.message;
          }
        } else if (error.status === 500) {
          errorMessage = 'Erreur serveur, veuillez réessayer plus tard';
          console.error('Erreur serveur détaillée:', error.error);
        }
        
        return throwError(() => new Error(errorMessage));
      })
    );
  }

  logout(): void {
    this.clearAuth();
    this.router.navigate(['/login']);
  }

  isAdmin(): boolean {
    return this.currentUser()?.role === 'ADMIN';
  }

  isBeneficiaire(): boolean {
    return this.currentUser()?.role === 'BENEFICIAIRE';
  }

  private decodeToken(token: string): DecodedToken {
    return jwtDecode<DecodedToken>(token);
  }

  private isTokenExpired(token: string): boolean {
    try {
      const decoded = this.decodeToken(token);
      return decoded.exp * 1000 < Date.now();
    } catch {
      return true;
    }
  }

  private clearAuth(): void {
    localStorage.removeItem('auth_token');
    this._currentUser.set(null);
    this._isAuthenticated.set(false);
    this._token.set(null);
  }

  requestPasswordReset(email: string): Observable<{ message: string }> {
    return this.apiService.post<{ message: string }>('auth/forgot-password', { email });
  }

  resetPassword(token: string, newPassword: string): Observable<{ message: string }> {
    return this.apiService.post<{ message: string }>('auth/reset-password', { token, newPassword });
  }
  //   forgotPassword(email: string): Observable<any> {
  //   return this.http.post(`${this.apiUrl}/reset-password`, { email });
  // }
// forgotPassword(email: string): Observable<any> {
//   // Simulation sans backend (fake delay + success)
//   return new Observable(observer => {
//     console.log('Simulated HTTP call to reset password for:', email);
//     setTimeout(() => {
//       observer.next({ message: 'Email sent successfully!' });
//       observer.complete();
//     }, 1000); // délai de 1 seconde pour simuler l'attente serveur
//   });
// }


}
