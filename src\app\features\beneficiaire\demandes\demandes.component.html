<div class="pad">
    <div class="back-title-container">
    <span class="back-arrow" (click)="onBack()">
    ←
    </span>
    <h2 class="page-title">Demande d'Appareil</h2>
</div>
    <div class="container mt-4">
      <div class="row justify-content-center">
        <div class="col-lg-8 col-md-10">
          <mat-card class="p-4">
            <mat-card-header class="mb-4">
              <mat-card-title class="text-center">Veuillez remplir le formulaire ci-dessous pour effectuer votre demande d'appareil:</mat-card-title>
            </mat-card-header>
            
            <mat-card-content>
              <form [formGroup]="deviceForm" (ngSubmit)="onSubmit()" class="device-form">
                
                <!-- Nom -->
                <div class="row mb-3">
                  <div class="col-12">
                    <mat-form-field appearance="outline" class="w-100">
                      <mat-label>Nom:</mat-label>
                      <input matInput formControlName="nom" placeholder="Entrez votre nom">
                      <mat-error *ngIf="deviceForm.get('nom')?.hasError('required')">
                        Le nom est requis
                      </mat-error>
                    </mat-form-field>
                  </div>
                </div>

                <!-- Prénom -->
                <div class="row mb-3">
                  <div class="col-12">
                    <mat-form-field appearance="outline" class="w-100">
                      <mat-label>Prénom:</mat-label>
                      <input matInput formControlName="prenom" placeholder="Entrez votre prénom">
                      <mat-error *ngIf="deviceForm.get('prenom')?.hasError('required')">
                        Le prénom est requis
                      </mat-error>
                    </mat-form-field>
                  </div>
                </div>

                <!-- Email -->
                <div class="row mb-3">
                  <div class="col-12">
                    <mat-form-field appearance="outline" class="w-100">
                      <mat-label>Email:</mat-label>
                      <input matInput type="email" formControlName="email" placeholder="Entrez votre email">
                      <mat-error *ngIf="deviceForm.get('email')?.hasError('required')">
                        L'email est requis
                      </mat-error>
                      <mat-error *ngIf="deviceForm.get('email')?.hasError('email')">
                        Format d'email invalide
                      </mat-error>
                    </mat-form-field>
                  </div>
                </div>

                <!-- Sexe -->
                <div class="row mb-3">
                  <div class="col-12">
                    <label class="form-label fw-bold">Sexe:</label>
                    <mat-radio-group formControlName="sexe" class="d-flex gap-4 mt-2">
                      <mat-radio-button value="masculin">Masculin</mat-radio-button>
                      <mat-radio-button value="feminin">Féminin</mat-radio-button>
                    </mat-radio-group>
                  </div>
                </div>

                <!-- Adresse -->
                <div class="row mb-3">
                  <div class="col-12">
                    <mat-form-field appearance="outline" class="w-100">
                      <mat-label>Adresse:</mat-label>
                      <textarea matInput formControlName="adresse" placeholder="Entrez votre adresse" rows="3"></textarea>
                      <mat-error *ngIf="deviceForm.get('adresse')?.hasError('required')">
                        L'adresse est requise
                      </mat-error>
                    </mat-form-field>
                  </div>
                </div>

                <!-- Date de naissance -->
                <div class="row mb-3">
                  <div class="col-12">
                    <mat-form-field appearance="outline" class="w-100">
                      <mat-label>Date de naissance:</mat-label>
                      <input matInput [matDatepicker]="picker" formControlName="dateNaissance">
                      <mat-datepicker-toggle matIconSuffix [for]="picker"></mat-datepicker-toggle>
                      <mat-datepicker #picker></mat-datepicker>
                      <mat-error *ngIf="deviceForm.get('dateNaissance')?.hasError('required')">
                        La date de naissance est requise
                      </mat-error>
                    </mat-form-field>
                  </div>
                </div>

                <!-- Statut -->
                <div class="row mb-3">
                  <div class="col-12">
                    <label class="form-label fw-bold">Statut:</label>
                    <mat-radio-group formControlName="statut" class="d-flex gap-4 mt-2">
                      <mat-radio-button value="ayant_droit">Ayant droit</mat-radio-button>
                      <mat-radio-button value="assure_amo">Assuré AMO</mat-radio-button>
                    </mat-radio-group>
                  </div>
                </div>

                <!-- Couverture -->
                <div class="row mb-3">
                  <div class="col-12">
                    <mat-form-field appearance="outline" class="w-100">
                      <mat-label>Couverture</mat-label>
                      <mat-select formControlName="couverture">
                        <mat-option value="cnss">CNSS</mat-option>
                        <mat-option value="cnops">CNOPS</mat-option>
                        <mat-option value="ramed">RAMED</mat-option>
                        <mat-option value="autre">Autre</mat-option>
                      </mat-select>
                      <mat-error *ngIf="deviceForm.get('couverture')?.hasError('required')">
                        La couverture est requise
                      </mat-error>
                    </mat-form-field>
                  </div>
                </div>

                <!-- Type -->
                <div class="row mb-3">
                  <div class="col-12">
                    <mat-form-field appearance="outline" class="w-100">
                      <mat-label>Type</mat-label>
                      <mat-select formControlName="type">
                        <mat-option value="prothese">Prothèse</mat-option>
                        <mat-option value="orthese">Orthèse</mat-option>
                        <mat-option value="fauteuil_roulant">Fauteuil roulant</mat-option>
                        <mat-option value="appareil_auditif">Appareil auditif</mat-option>
                        <mat-option value="autre">Autre</mat-option>
                      </mat-select>
                      <mat-error *ngIf="deviceForm.get('type')?.hasError('required')">
                        Le type est requis
                      </mat-error>
                    </mat-form-field>
                  </div>
                </div>

                <!-- Téléversement de justificatifs -->
                <div class="row mb-4">
                  <div class="col-12">
                    <label class="form-label fw-bold mb-3">Téléversement de justificatifs:</label>
                    
                    <div class="upload-section">
                      <!-- CIN -->
                      <div class="upload-item mb-3">
                        <button type="button" mat-stroked-button class="upload-btn w-100" 
                                (click)="fileInputCIN.click()">
                          <mat-icon>upload</mat-icon>
                          Déposer copie de CIN
                        </button>
                        <input #fileInputCIN type="file" hidden accept=".pdf,.jpg,.jpeg,.png" 
                               (change)="onFileSelect($event, 'cin')">
                        <div *ngIf="uploadedFiles.cin" class="file-info mt-2">
                          <small class="text-success">
                            <mat-icon class="small-icon">check_circle</mat-icon>
                            {{uploadedFiles.cin.name}}
                          </small>
                        </div>
                      </div>

                      <!-- AMO -->
                      <div class="upload-item mb-3">
                        <button type="button" mat-stroked-button class="upload-btn w-100" 
                                (click)="fileInputAMO.click()">
                          <mat-icon>upload</mat-icon>
                          Déposer copie de AMO
                        </button>
                        <input #fileInputAMO type="file" hidden accept=".pdf,.jpg,.jpeg,.png" 
                               (change)="onFileSelect($event, 'amo')">
                        <div *ngIf="uploadedFiles.amo" class="file-info mt-2">
                          <small class="text-success">
                            <mat-icon class="small-icon">check_circle</mat-icon>
                            {{uploadedFiles.amo.name}}
                          </small>
                        </div>
                      </div>

                      <!-- Certificat médical -->
                      <div class="upload-item mb-3">
                        <button type="button" mat-stroked-button class="upload-btn w-100" 
                                (click)="fileInputMedical.click()">
                          <mat-icon>upload</mat-icon>
                          Déposer copie de certificat médical
                        </button>
                        <input #fileInputMedical type="file" hidden accept=".pdf,.jpg,.jpeg,.png" 
                               (change)="onFileSelect($event, 'medical')">
                        <div *ngIf="uploadedFiles.medical" class="file-info mt-2">
                          <small class="text-success">
                            <mat-icon class="small-icon">check_circle</mat-icon>
                            {{uploadedFiles.medical.name}}
                          </small>
                        </div>
                      </div>

                      <!-- Certificat d'handicap -->
                      <div class="upload-item mb-3">
                        <button type="button" mat-stroked-button class="upload-btn w-100" 
                                (click)="fileInputHandicap.click()">
                          <mat-icon>upload</mat-icon>
                          Déposer copie de certificat d'handicape
                        </button>
                        <input #fileInputHandicap type="file" hidden accept=".pdf,.jpg,.jpeg,.png" 
                               (change)="onFileSelect($event, 'handicap')">
                        <div *ngIf="uploadedFiles.handicap" class="file-info mt-2">
                          <small class="text-success">
                            <mat-icon class="small-icon">check_circle</mat-icon>
                            {{uploadedFiles.handicap.name}}
                          </small>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>

                <!-- Bouton Suivant -->
                <div class="row">
                  <div class="col-12 text-center">
                    <button type="submit" mat-raised-button  class="submit-btn px-5 py-2"
                            [disabled]="!deviceForm.valid">
                      Suivant
                    </button>
                  </div>
                </div>

              </form>
            </mat-card-content>
          </mat-card>
        </div>
      </div>
    </div>
    </div>
