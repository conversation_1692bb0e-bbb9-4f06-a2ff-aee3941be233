import { Component , OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormBuilder, FormGroup, Validators , ReactiveFormsModule } from '@angular/forms';
import { MatSnackBar } from '@angular/material/snack-bar';

// Angular Material Modules
import { MatCardModule } from '@angular/material/card';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatSelectModule } from '@angular/material/select';
import { MatRadioModule } from '@angular/material/radio';
import { MatCheckboxModule } from '@angular/material/checkbox';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatDatepickerModule } from '@angular/material/datepicker';
import { MatNativeDateModule } from '@angular/material/core';
import { MatSnackBarModule } from '@angular/material/snack-bar';

interface UploadedFiles {
  cv?: File;
  diploma?: File;
  license?: File;
  photo?: File;
}

@Component({
  selector: 'app-participation',
  imports: [
    CommonModule,
    ReactiveFormsModule,
    
    // Angular Material
    MatCardModule,
    MatFormFieldModule,
    MatInputModule,
    MatSelectModule,
    MatRadioModule,
    MatCheckboxModule,
    MatButtonModule,
    MatIconModule,
    MatDatepickerModule,
    MatNativeDateModule,
    MatSnackBarModule
  ],
  templateUrl: './participation.component.html',
  styleUrl: './participation.component.scss'
})
export class ParticipationComponent  implements OnInit{
    participationForm!: FormGroup;
  uploadedFiles: UploadedFiles = {};
  
  specialites = [
    { value: 'cardiologie', label: 'Cardiologie' },
    { value: 'neurologie', label: 'Neurologie' },
    { value: 'pediatrie', label: 'Pédiatrie' },
    { value: 'chirurgie', label: 'Chirurgie' },
    { value: 'dermatologie', label: 'Dermatologie' },
    { value: 'psychiatrie', label: 'Psychiatrie' },
    { value: 'radiologie', label: 'Radiologie' },
    { value: 'anesthesie', label: 'Anesthésie-Réanimation' },
    { value: 'autre', label: 'Autre' }
  ];

  typesParticipation = [
    { value: 'conference', label: 'Conférence' },
    { value: 'atelier', label: 'Atelier pratique' },
    { value: 'formation', label: 'Formation continue' },
    { value: 'symposium', label: 'Symposium' },
    { value: 'poster', label: 'Présentation poster' }
  ];

  constructor(
    private fb: FormBuilder,
    private snackBar: MatSnackBar
  ) {}

  ngOnInit(): void {
    this.initializeForm();
  }

  private initializeForm(): void {
    this.participationForm = this.fb.group({
      // Informations personnelles
      nom: ['', [Validators.required, Validators.minLength(2)]],
      prenom: ['', [Validators.required, Validators.minLength(2)]],
      email: ['', [Validators.required, Validators.email]],
      telephone: ['', [Validators.required, Validators.pattern(/^[0-9]{10}$/)]],
      dateNaissance: ['', Validators.required],
      sexe: ['', Validators.required],
      
      // Informations professionnelles
      profession: ['', Validators.required],
      specialite: ['', Validators.required],
      etablissement: ['', Validators.required],
      adresseEtablissement: ['', Validators.required],
      anneesExperience: ['', [Validators.required, Validators.min(0)]],
      
      // Participation
      typeParticipation: ['', Validators.required],
      motivations: ['', [Validators.required, Validators.minLength(50)]],
      attentes: ['', [Validators.required, Validators.minLength(30)]],
      
      // Disponibilité
      disponibiliteComplete: [false],
      datesIndisponibles: [''],
      
      // Besoins spéciaux
      besoinsSpeciaux: [''],
      regimeAlimentaire: [''],
      
      // Acceptation
      accepteConditions: [false, Validators.requiredTrue],
      accepteNewsletter: [false]
    });
  }

  onFileSelect(event: Event, fileType: keyof UploadedFiles): void {
    const input = event.target as HTMLInputElement;
    if (input.files && input.files.length > 0) {
      const file = input.files[0];
      
      // Vérification de la taille (max 5MB)
      if (file.size > 5 * 1024 * 1024) {
        this.snackBar.open('Le fichier ne doit pas dépasser 5MB', 'Fermer', {
          duration: 3000,
          panelClass: ['error-snackbar']
        });
        return;
      }
      
      this.uploadedFiles[fileType] = file;
      this.snackBar.open(`${file.name} téléversé avec succès`, 'Fermer', {
        duration: 2000,
        panelClass: ['success-snackbar']
      });
    }
  }

  removeFile(fileType: keyof UploadedFiles): void {
    delete this.uploadedFiles[fileType];
    this.snackBar.open('Fichier supprimé', 'Fermer', {
      duration: 2000
    });
  }

  onSubmit(): void {
    if (this.participationForm.valid) {
      const formData = new FormData();
      
      // Ajouter les données du formulaire
      Object.keys(this.participationForm.value).forEach(key => {
        const value = this.participationForm.get(key)?.value;
        if (value !== null && value !== undefined) {
          formData.append(key, value.toString());
        }
      });
      
      // Ajouter les fichiers
      Object.keys(this.uploadedFiles).forEach(key => {
        const file = this.uploadedFiles[key as keyof UploadedFiles];
        if (file) {
          formData.append(key, file);
        }
      });
      
      // Simulation de l'envoi
      this.simulateSubmission(formData);
    } else {
      this.markFormGroupTouched();
      this.snackBar.open('Veuillez corriger les erreurs dans le formulaire', 'Fermer', {
        duration: 3000,
        panelClass: ['error-snackbar']
      });
    }
  }

  private simulateSubmission(formData: FormData): void {
    // Simulation d'un envoi asynchrone
    setTimeout(() => {
      this.snackBar.open('Votre demande de participation a été envoyée avec succès!', 'Fermer', {
        duration: 5000,
        panelClass: ['success-snackbar']
      });
      console.log('Données envoyées:', Object.fromEntries(formData.entries()));
    }, 1000);
  }

  private markFormGroupTouched(): void {
    Object.keys(this.participationForm.controls).forEach(key => {
      const control = this.participationForm.get(key);
      control?.markAsTouched();
    });
  }

  onBack(): void {
    // Logique de retour - à adapter selon votre navigation
    window.history.back();
  }

  // Getters pour faciliter l'accès aux contrôles dans le template
  get nom() { return this.participationForm.get('nom'); }
  get prenom() { return this.participationForm.get('prenom'); }
  get email() { return this.participationForm.get('email'); }
  get telephone() { return this.participationForm.get('telephone'); }
  get profession() { return this.participationForm.get('profession'); }
  get specialite() { return this.participationForm.get('specialite'); }
  get etablissement() { return this.participationForm.get('etablissement'); }
  get motivations() { return this.participationForm.get('motivations'); }
  get attentes() { return this.participationForm.get('attentes'); }
  get accepteConditions() { return this.participationForm.get('accepteConditions'); }
}

