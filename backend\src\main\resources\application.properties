# Configuration du serveur
server.port=8080

# Configuration de la base de données
spring.datasource.url=*****************************************************************************
spring.datasource.username=root
spring.datasource.password=Hamza1999
spring.datasource.driver-class-name=com.mysql.cj.jdbc.Driver

# Configuration JPA/Hibernate
spring.jpa.hibernate.ddl-auto=update
spring.jpa.show-sql=true
spring.jpa.properties.hibernate.format_sql=true
spring.jpa.properties.hibernate.dialect=org.hibernate.dialect.MySQL8Dialect

# Configuration JWT
app.jwt.secret=votreCleSecreteTresLongueEtTresSecuriseeQuiDoitEtreChangeeEnProduction
app.jwt.expiration=86400000

# Configuration Cross-Origin (CORS)
spring.web.cors.allowed-origins=http://localhost:4200
spring.web.cors.allowed-methods=GET,POST,PUT,DELETE,OPTIONS
spring.web.cors.allowed-headers=*

# Configuration des messages d'erreur
server.error.include-message=always
server.error.include-binding-errors=always 