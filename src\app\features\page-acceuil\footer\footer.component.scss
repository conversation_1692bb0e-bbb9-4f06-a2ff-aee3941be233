.footer {
  background: #2c3e50;
  color: white;
  position: relative;
  z-index: 50;
  //   margin-top: 0;
  // padding-top: 1rem;

  // Animation optionnelle (désactivée pour éviter les problèmes)
  // transform: translateY(100%);
  // transition: transform 0.4s ease-out;
  // &.visible {
  //   transform: translateY(0);
  // }
}

  .footer-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 2rem;
    max-width: 1200px;
    margin: 0 auto;
    // align-items: center;
    
  }

  .footer-col {
    h3 {
      color: #3498db;
      margin-bottom: 1rem;
    }

    a {
      display: block;
      color: #ecf0f1;
      margin-bottom: 0.5rem;
      text-decoration: none;
      position: relative;
      font-weight: 500;

      // Visited state - garde le vert pour tous les liens sauf Contact
      &:visited {
        color: #2e7d32;
      }

      // Hover underline
      &::after {
        content: '';
        position: absolute;
        left: 0;
        bottom: -2px;
        height: 2px;
        width: 0;
        background-color: #2e7d32;
        transition: width 0.3s ease;
      }

      &:hover::after {
        width: 100%;
      }

      &:hover {
        color: #2e7d32;
      }

      // Optional: when clicked (active)
      &:active {
        color: #1b5e20;
      }

      // Style spécifique pour le lien Contact - reste blanc par défaut
      &[routerLink="/contact"] {
        color: #ecf0f1 !important;

        &:visited {
          color: #ecf0f1 !important;
        }

        &:hover {
          color: #2e7d32 !important;
        }

        &:active {
          color: #1b5e20 !important;
        }
      }
    }
  }

.brand {
  display: flex;
  flex-direction: column; // <= colonne au lieu de ligne
  align-items: center;
  gap: 0.5rem;
  margin-bottom: 1rem;
  font-size: 1.2rem;
  font-weight: bold;
  text-align: center;
  // margin-top: 3rem;
}
span{
      color: #3498db;
      // margin-bottom: 1rem;
}


  .social-icons {
    display: flex;
    gap: 1rem;

    a {
      color: white;
      transition: transform 0.3s;
      position: relative;
      font-weight: 500;

      // Visited state
      &:visited {
        color: #2e7d32;
      }

      // Hover underline
      &::after {
        content: '';
        position: absolute;
        left: 0;
        bottom: -2px;
        height: 2px;
        width: 0;
        background-color: #2e7d32;
        transition: width 0.3s ease;
      }

      &:hover::after {
        width: 100%;
      }

      &:hover {
        transform: translateY(-3px);
        color: #2e7d32;
      }

      // Optional: when clicked (active)
      &:active {
        color: #1b5e20;
      }
    }
  }

  .copyright {
    text-align: center;
    margin-top: -1.5rem;
    padding-top: 1rem;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
    font-size: 0.9rem;
  }
.align-middle{
  color:white;
  font-weight: 500;

  // Visited state
  &:visited {
    color: #2e7d32;
  }

  &:hover{
    color: #2e7d32;
  }

  // Optional: when clicked (active)
  &:active {
    color: #1b5e20;
  }
}

// Styles for social media links in the footer
.container-fluid {
  a.text-decoration-none {
    position: relative;
    font-weight: 500;
    color: white;
    display: inline-block;

    // Visited state
    &:visited {
      color: #2e7d32;
    }

    // Hover underline
    &::after {
      content: '';
      position: absolute;
      left: 0;
      bottom: -2px;
      height: 2px;
      width: 0;
      background-color: #2e7d32;
      transition: width 0.3s ease;
    }

    &:hover::after {
      width: 100%;
    }

    &:hover {
      color: #2e7d32;
    }

    // Optional: when clicked (active)
    &:active {
      color: #1b5e20;
    }

    i {
      transition: color 0.3s ease;
    }
  }
}

// Styles for contact buttons in footer
.contact-button {
  position: relative;
  font-weight: 500;

  // Visited state
  &:visited {
    color: #2e7d32;
  }

  // Hover underline
  &::after {
    content: '';
    position: absolute;
    left: 0;
    bottom: -2px;
    height: 2px;
    width: 0;
    background-color: #2e7d32;
    transition: width 0.3s ease;
  }

  &:hover::after {
    width: 100%;
  }

  &:hover {
    color: #2e7d32;

    .align-middle {
      color: #2e7d32;
    }
  }

  // Optional: when clicked (active)
  &:active {
    color: #1b5e20;
  }
}