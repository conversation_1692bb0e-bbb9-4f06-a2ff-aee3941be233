import { Component , HostListener } from '@angular/core';
import { CommonModule } from '@angular/common';
import { MatToolbarModule } from '@angular/material/toolbar';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatTableModule } from '@angular/material/table';
import { MatMenuModule } from '@angular/material/menu';

// interface beneficiaire
interface Beneficiaire {
  id: string;
  nom: string;
  prenom: string;
  adresse: string;
  dateNaissance: string;
  telephone: string;
  appareil: string;
}

@Component({
  selector: 'app-gestion-beneficiaires',
  imports: [ CommonModule, MatToolbarModule, MatButtonModule, MatIconModule, MatTableModule, MatMenuModule ],
  templateUrl: './gestion-beneficiaires.component.html',
  styleUrl: './gestion-beneficiaires.component.scss'
})
export class GestionBeneficiairesComponent {

  beneficiaires: Beneficiaire[] = [
  {
    id: 'BEN-2023-001',
    nom: '<PERSON>',
    prenom: 'Fatima',
    adresse: '12 Rue des Orangers, <PERSON><PERSON><PERSON><PERSON>, Khenifra',
    dateNaissance: '15/03/1978',
    telephone: '0612345678',
    appareil: 'Lunettes progressives'
    
  },
  {
    id: 'BEN-2023-002',
    nom: 'Ben Salah',
    prenom: 'Mohamed',
    adresse: '45 Avenue Mohammed V, Résidence Al Amal, Khenifra',
    dateNaissance: '22/07/1965',
    telephone: '0678451236',
    appareil: 'Appareil auditif numérique'
  },
  {
    id: 'BEN-2023-003',
    nom: 'Zouhairi',
    prenom: 'Amina',
    adresse: 'N°7 Lotissement Al Wahda, Khenifra',
    dateNaissance: '30/11/1982',
    telephone: '0654789412',
    appareil: 'Fauteuil roulant manuel'
  },
  {
    id: 'BEN-2023-004',
    nom: 'Lahbabi',
    prenom: 'Youssef',
    adresse: 'Résidence Essalam, Appt 12, Khenifra',
    dateNaissance: '05/01/1950',
    telephone: '0698745632',
    appareil: 'Prothèse fémorale'
  },
  {
    id: 'BEN-2023-005',
    nom: 'Cherkaoui',
    prenom: 'Khadija',
    adresse: '18 Rue Ibn Sina, Quartier Administratif, Khenifra',
    dateNaissance: '14/09/1995',
    telephone: '0625147896',
    appareil: 'Lunettes de soleil médicales'
  },
  {
    id: 'BEN-2023-006',
    nom: 'Mouline',
    prenom: 'Rachid',
    adresse: 'Complexe Dakhla, Bloc C, Khenifra',
    dateNaissance: '03/05/1988',
    telephone: '0687954123',
    appareil: 'Orthèse plantaire sur mesure'
  },
  {
    id: 'BEN-2023-007',
    nom: 'Bennis',
    prenom: 'Samira',
    adresse: '24 Avenue Moulay Youssef, Khenifra',
    dateNaissance: '19/12/1973',
    telephone: '0612458796',
    appareil: 'Canne blanche électronique'
  },
  {
    id: 'BEN-2023-008',
    nom: 'Idrissi',
    prenom: 'Hassan',
    adresse: 'Lotissement Al Falah, N°32, Khenifra',
    dateNaissance: '08/04/1960',
    telephone: '0678125496',
    appareil: 'Appareil auditif contour d\'oreille'
  },
    {
    id: 'BEN-2023-009',
    nom: 'hachimi',
    prenom: 'Hassan',
    adresse: 'Lotissement Al afoud, N°39, Khenifra',
    dateNaissance: '08/04/1970',
    telephone: '0678125496',
    appareil: 'Appareil auditif contour d\'oreille'
  },
    {
    id: 'BEN-2023-010',
    nom: 'hamzaoui',
    prenom: 'rabie',
    adresse: 'Lotissement Al qadeh, N°92, Khenifra',
    dateNaissance: '08/04/1960',
    telephone: '0670125496',
    appareil: 'Appareil auditif contour d\'oreille'
  }
];

  supprimerBeneficiaire(id: string) {
    if (confirm('Êtes-vous sûr de vouloir supprimer ce bénéficiaire ?')) {
      this.beneficiaires = this.beneficiaires.filter(b => b.id !== id);
    }
  }

    onBack() {
  // Par exemple : revenir à la page précédente
  history.back(); // ou this.router.navigate(['/autre-page']);
}

}
