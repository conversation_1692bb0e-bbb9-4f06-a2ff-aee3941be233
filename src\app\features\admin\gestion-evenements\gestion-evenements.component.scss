.container-fluid{
    margin-top: 7rem;
}
.back-title-container {
  display: flex;
  align-items: center;
  margin-bottom: 1rem;
  gap: 0.5rem;
  padding: 0 2rem;
  margin-top: 3rem;
}

.back-arrow {
  font-size: 1.5rem;
  cursor: pointer;
  color: #3498db;
  transition: color 0.3s;

  &:hover {
    color: #2ecc71;
  }
}

.page-title {
  font-size: 1.5rem;
  font-weight: 600;
  color: #2c3e50;
  margin: 0;
}
    .photo-placeholder {
      width: 40px;
      height: 40px;
      border: 1px solid #ddd;
      border-radius: 4px;
      display: flex;
      align-items: center;
      justify-content: center;
      background-color: #f5f5f5;
    }

    .mat-mdc-table {
      width: 100%;
    }

    .mat-mdc-header-cell, .mat-mdc-cell {
      padding: 8px;
    }

    .container {
      max-width: 1200px;
    }

    mat-form-field {
      margin-bottom: 16px;
    }

    .mat-mdc-card {
      margin-bottom: 24px;
    }