import { HttpErrorResponse, HttpInterceptorFn } from '@angular/common/http';
import { catchError } from 'rxjs/operators';
import { throwError } from 'rxjs';

export const errorInterceptor: HttpInterceptorFn = (req, next) => {
  return next(req).pipe(
    catchError((error: HttpErrorResponse) => {
      let errorMessage = 'Une erreur inconnue s\'est produite';

      // Log détaillé de l'erreur
      console.error('Détails de l\'erreur HTTP :', {
        url: req.url,
        method: req.method,
        headers: req.headers,
        body: req.body,
        error: error
      });

      if (error.error instanceof ErrorEvent) {
        // Erreur côté client
        errorMessage = `Erreur: ${error.error.message}`;
      } else {
        // Erreur côté serveur
        switch (error.status) {
          case 400:
            errorMessage = error.error?.message || 'Requête invalide';
            break;
          case 401:
            errorMessage = 'Non autorisé - Veuillez vous connecter';
            break;
          case 403:
            errorMessage = 'Accès refusé';
            break;
          case 404:
            errorMessage = 'Ressource non trouvée';
            break;
          case 500:
            errorMessage = error.error?.message || 'Erreur serveur - Veuillez réessayer plus tard';
            break;
          case 0:
            errorMessage = 'Impossible de joindre le serveur. Vérifiez votre connexion et que le serveur est en cours d\'exécution.';
            break;
          default:
            errorMessage = `Erreur ${error.status}: ${error.error?.message || error.statusText}`;
        }
      }

      return throwError(() => new Error(errorMessage));
    })
  );
}; 