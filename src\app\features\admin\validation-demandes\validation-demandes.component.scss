    .container-fluid{
        margin-top: 7rem;
    }
    .stats-card {
      height: 120px;
      display: flex;
      align-items: center;
      justify-content: center;
      transition: transform 0.2s;
    }
    
    .stats-card:hover {
      transform: translateY(-2px);
      box-shadow: 0 4px 8px rgba(0,0,0,0.1);
    }
    
    .stats-icon {
      font-size: 32px;
      margin-bottom: 8px;
    }
    
    .demande-card {
      margin-bottom: 16px;
      transition: all 0.3s ease;
    }
    
    .demande-card:hover {
      box-shadow: 0 4px 12px rgba(0,0,0,0.15);
    }
    
    .demande-card.en-attente {
      border-left: 4px solid #ff9800;
    }
    
    .demande-card.approuve {
      border-left: 4px solid #4caf50;
    }
    
    .demande-card.rejete {
      border-left: 4px solid #f44336;
    }
    
    .info-section {
      margin-bottom: 16px;
    }
    
    .info-section h6 {
      color: #555;
      margin-bottom: 12px;
      display: flex;
      align-items: center;
    }
    
    .info-item {
      margin-bottom: 8px;
      padding: 4px 0;
    }
    
    .documents-section h6 {
      color: #555;
      margin-bottom: 12px;
      display: flex;
      align-items: center;
    }
    
    .status-badge .mat-chip {
      font-weight: 500;
    }
    
    .status-chip-en-attente {
      background-color: #fff3e0;
      color: #ef6c00;
    }
    
    .status-chip-approuve {
      background-color: #e8f5e8;
      color: #2e7d32;
    }
    
    .status-chip-rejete {
      background-color: #ffebee;
      color: #c62828;
    }
    
    .date-info {
      border-top: 1px solid #eee;
      padding-top: 12px;
    }