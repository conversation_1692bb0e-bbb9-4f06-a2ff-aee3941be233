export interface Appareillage {
  id: number;
  nom: string;
  description: string;
  categorie: string;
  quantite: number;
  prix?: number;
}

export class AppareillageModel implements Appareillage {
  constructor(
    public id: number,
    public nom: string,
    public description: string,
    public categorie: string,
    public quantite: number,
    public prix?: number
  ) {}

  isDisponible(): boolean {
    return this.quantite > 0;
  }

  getFormattedPrice(): string {
    return this.prix ? `${this.prix.toFixed(2)} MAD` : 'Gratuit';
  }
}
