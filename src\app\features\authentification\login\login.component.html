<div class="auth-container">
  <div class="auth-card">
    <div class="background-image">
      <h1>Bienvenu</h1>
    </div>
    <div class="form-container">
      <a routerLink="/" class="back-button">
        ←
      </a>
      <h2>Connexion</h2>
      <p class="subtitle">Bienvenu. Veuillez vous connecter !</p>
      
      <form [formGroup]="loginForm" (ngSubmit)="onSubmit()" autocomplete="off">
        <div class="form-group">
          <label for="usernameOrEmail">CIN ou Email</label>
          <input type="text" id="usernameOrEmail" formControlName="usernameOrEmail" placeholder="Entrez votre CIN ou email" autocomplete="off" autocapitalize="off" autocorrect="off" spellcheck="false">
          <div *ngIf="loginForm.get('usernameOrEmail')?.invalid && (loginForm.get('usernameOrEmail')?.dirty || loginForm.get('usernameOrEmail')?.touched)" class="error-message">
            <div *ngIf="loginForm.get('usernameOrEmail')?.errors?.['required']">CIN ou email est requis</div>
          </div>
        </div>
        
        <div class="form-group">
          <label for="password">Mot de passe</label>
          <div class="password-input">
            <input [type]="hidePassword ? 'password' : 'text'" id="password" formControlName="password" placeholder="Entrez votre mot de passe" autocomplete="new-password" autocapitalize="off" autocorrect="off" spellcheck="false">
            <button type="button" class="toggle-password" (click)="togglePasswordVisibility()">
              <mat-icon>{{ hidePassword ? 'visibility_off' : 'visibility' }}</mat-icon>
            </button>
          </div>

          <div *ngIf="loginForm.get('password')?.invalid && (loginForm.get('password')?.dirty || loginForm.get('password')?.touched)" class="error-message">
            <div *ngIf="loginForm.get('password')?.errors?.['required']">Mot de passe est requis</div>
            <div *ngIf="loginForm.get('password')?.errors?.['minlength']">Le mot de passe doit contenir au moins 6 caractères</div>
          </div>
        </div>
        
        <div *ngIf="error" class="error-alert">
          <mat-icon class="error-icon">error</mat-icon>
          <span>{{ error }}</span>
        </div>

        <div class="mb-4">
          <div class="text-end auth-links">
            <a href="#" class="small" routerLink="/forgot-password">Mot de passe oublié?</a>
          </div>
        </div>
        
        <button type="submit" class="submit-button" [disabled]="loginForm.invalid || loading">
          {{ loading ? 'Connexion en cours...' : 'Se connecter' }}
        </button>
        
        <div class="auth-links">
          <p>Vous n'avez pas de compte? <a routerLink="/register">S'inscrire</a></p>
        </div>
      </form>
    </div>
  </div>
</div>

