import { Component , OnInit  } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormBuilder, FormGroup, FormsModule, Validators , ReactiveFormsModule } from '@angular/forms';
import { MatDialog, MatDialogModule } from '@angular/material/dialog';
import { MatSnackBar, MatSnackBarModule } from '@angular/material/snack-bar';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatRadioModule } from '@angular/material/radio';
import { MatButtonModule } from '@angular/material/button';
import { MatSelectModule } from '@angular/material/select';
import { MatDatepickerModule } from '@angular/material/datepicker';
import { MatNativeDateModule } from '@angular/material/core';
import { MatCardModule } from '@angular/material/card';
import { MatIconModule } from '@angular/material/icon';
import { MatTableModule } from '@angular/material/table';
import { MatTooltipModule } from '@angular/material/tooltip';

export interface Appareil {
  id: number;
  typeAppareil: string;
  description: string;
  quantiteDisponible: number;
  societeFournissante: string;
  photo?: string;
}

@Component({
  selector: 'app-gestion-stock',
  standalone : true,
  imports: [ MatFormFieldModule, CommonModule, ReactiveFormsModule,
    MatRadioModule, MatButtonModule, 
    MatSelectModule, MatDatepickerModule, 
    MatNativeDateModule, MatCardModule, 
    MatFormFieldModule,
    MatInputModule,
    MatButtonModule,
    MatIconModule,
    MatTableModule,
    MatTooltipModule,
    MatSnackBarModule,
    MatDialogModule ,
    FormsModule,],
  templateUrl: './gestion-stock.component.html',
  styleUrl: './gestion-stock.component.scss'
})
export class GestionStockComponent implements OnInit {

  appareilForm: FormGroup;
  appareils: Appareil[] = [
    {
      id: 1,
      typeAppareil: 'Lunette',
      description: 'Délégation provinciale de la santé',
      quantiteDisponible: 280,
      societeFournissante: 'VueWell',
      photo: 'assets/images/lunette.jpg'
    },
    {
      id: 2,
      typeAppareil: 'Appareils auditives',
      description: 'Délégation provinciale de la santé',
      quantiteDisponible: 340,
      societeFournissante: 'WeHear',
      photo: 'assets/images/appareil-auditif.jpg'
    }
  ];
  
  displayedColumns: string[] = ['id', 'typeAppareil', 'description', 'quantiteDisponible', 'societeFournissante', 'photo', 'actions'];
  selectedFile: File | null = null;
  nextId = 3;

  constructor(
    private fb: FormBuilder,
    private dialog: MatDialog,
    private snackBar: MatSnackBar
  ) {
    this.appareilForm = this.fb.group({
      typeAppareil: ['', Validators.required],
      description: ['', Validators.required],
      quantiteDisponible: ['', [Validators.required, Validators.min(1)]],
      societeFournissante: ['', Validators.required]
    });
  }

  ngOnInit(): void {}

  onFileSelected(event: any): void {
    const file = event.target.files[0];
    if (file) {
      this.selectedFile = file;
    }
  }

  creerAppareil(): void {
    if (this.appareilForm.valid) {
      const nouvelAppareil: Appareil = {
        id: this.nextId++,
        typeAppareil: this.appareilForm.value.typeAppareil,
        description: this.appareilForm.value.description,
        quantiteDisponible: this.appareilForm.value.quantiteDisponible,
        societeFournissante: this.appareilForm.value.societeFournissante,
        photo: this.selectedFile ? URL.createObjectURL(this.selectedFile) : undefined
      };

      this.appareils.push(nouvelAppareil);
      this.appareilForm.reset();
      this.selectedFile = null;
      
      this.snackBar.open('Appareil ajouté avec succès!', 'Fermer', {
        duration: 3000,
        panelClass: ['success-snackbar']
      });
    } else {
      this.snackBar.open('Veuillez remplir tous les champs requis', 'Fermer', {
        duration: 3000,
        panelClass: ['error-snackbar']
      });
    }
  }

  modifier(appareil: Appareil): void {
    // Logique pour modifier l'appareil
    console.log('Modifier appareil:', appareil);
    this.snackBar.open('Fonction de modification à implémenter', 'Fermer', {
      duration: 2000
    });
  }

  supprimer(id: number): void {
    const index = this.appareils.findIndex(a => a.id === id);
    if (index > -1) {
      this.appareils.splice(index, 1);
      this.snackBar.open('Appareil supprimé avec succès!', 'Fermer', {
        duration: 3000,
        panelClass: ['success-snackbar']
      });
    }
  }

  voir(appareil: Appareil): void {
    // Logique pour voir les détails de l'appareil
    console.log('Voir appareil:', appareil);
    this.snackBar.open('Fonction de visualisation à implémenter', 'Fermer', {
      duration: 2000
    });
  }

  onBack() {
  // Par exemple : revenir à la page précédente
  history.back(); // ou this.router.navigate(['/autre-page']);
}

}
