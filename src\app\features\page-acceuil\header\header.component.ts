import { Component, HostListener, Input } from '@angular/core';
import { RouterLink } from '@angular/router';
import { CommonModule } from '@angular/common';
import { MatIconModule } from '@angular/material/icon';

@Component({
  selector: 'app-header',
  standalone: true,
  imports: [RouterLink, CommonModule, MatIconModule],
  templateUrl: './header.component.html',
  styleUrl: './header.component.scss'
})
export class HeaderComponent {
  @Input() additionalLinks: { path: string, label: string }[] = [];
  @Input() additionalButtons: { path: string, label: string, class: string }[] = [];

  @Input() role: 'public' | 'admin' | 'beneficiaire' = 'public';

  get navLinks() {
    switch (this.role) {
      case 'admin':
        return [
          { label: 'Accueil', link: '/admin/admin-home' },
          { label: 'Gestion événements', link: '/admin/gestion-evenements' },
          { label: 'Gestion bénéficiaires', link: '/admin/gestion-beneficiaires' },
          { label: 'Gestion appareillages', link: '/admin/gestion-stock' },
          { label: 'Validation demandes', link: '/admin/validation-demandes' },
        ];
      case 'beneficiaire':
        return [
          { label: 'Accueil', link: '/beneficiaire/beneficiaire-home' },
          { label: 'Demande Appareil', link: '/beneficiaire/demandes' },
          { label: 'Suivi Demande', link: '/beneficiaire/suivi-demande' },
          { label: 'Mes documents', link: '/beneficiaire/documents' },
          { label: 'participation aux événements', link: '/beneficiaire/participation' },
        ];
      default:
        return [
          { label: 'Accueil', link: '/' },
          { label: 'A propos', link: '/a-propos' },
          { label: 'Contact', link: '#footer' },
        ];
    }
  }

  get showAuthButtons() {
    return this.role === 'public';
  }

  showProfileDropdown = false;

  toggleDropdown() {
    this.showProfileDropdown = !this.showProfileDropdown;
  }

  @HostListener('document:click', ['$event'])
  handleClickOutside(event: MouseEvent) {
    const target = event.target as HTMLElement;
    if (!target.closest('.profile-circle-btn') && !target.closest('.profile-dropdown-menu')) {
      this.showProfileDropdown = false;
    }
  }

  isScrolled = false;

  @HostListener('window:scroll', [])
  onWindowScroll() {
    const scrollY = window.pageYOffset || document.documentElement.scrollTop || document.body.scrollTop || 0;
    this.isScrolled = scrollY > 100;
  }

  getChangePasswordLink(): string {
    if (this.role === 'admin') {
      return '/admin/change-password';
    } else if (this.role === 'beneficiaire') {
      return '/beneficiaire/change-password';
    } else {
      return '/change-password';
    }
  }

  scrollToFooter(): void {
    const footer = document.getElementById('footer');
    if (footer) {
      footer.scrollIntoView({ behavior: 'smooth' });
    }
  }
}
