import { Injectable } from '@angular/core';
import { HttpClient, HttpErrorResponse } from '@angular/common/http';
import { Observable, throwError } from 'rxjs';
import { catchError } from 'rxjs/operators';
import { environment } from '../../../environments/environment';

@Injectable({ providedIn: 'root' })
export class ApiService {
  private baseUrl = environment.apiUrl;

  constructor(private http: HttpClient) {}

  private handleError(error: HttpErrorResponse) {
    console.error('Une erreur s\'est produite:', error);
    
    if (error.error instanceof ErrorEvent) {
      // Erreur côté client
      console.error('Erreur côté client:', error.error.message);
      return throwError(() => new Error('Problème de connexion internet. Veuillez vérifier votre connexion.'));
    } else {
      // Erreur côté backend
      console.error(
        `Code d'erreur backend ${error.status}, ` +
        `corps: ${JSON.stringify(error.error)}`);
      
      if (error.status === 0) {
        return throwError(() => new Error('Impossible de joindre le serveur. Veuillez vérifier que le backend est en cours d\'exécution.'));
      }
      
      // Retourner le message d'erreur du backend s'il existe
      const message = error.error?.message || 'Erreur serveur, veuillez réessayer plus tard';
      return throwError(() => new Error(message));
    }
  }

  get<T>(endpoint: string): Observable<T> {
    return this.http.get<T>(`${this.baseUrl}/${endpoint}`).pipe(
      catchError(this.handleError)
    );
  }

  post<T>(endpoint: string, data: any): Observable<T> {
    console.log('Envoi de la requête POST à:', `${this.baseUrl}/${endpoint}`);
    console.log('Données envoyées:', data);
    
    return this.http.post<T>(`${this.baseUrl}/${endpoint}`, data).pipe(
      catchError(this.handleError)
    );
  }

  put<T>(endpoint: string, data: any): Observable<T> {
    return this.http.put<T>(`${this.baseUrl}/${endpoint}`, data).pipe(
      catchError(this.handleError)
    );
  }

  delete<T>(endpoint: string): Observable<T> {
    return this.http.delete<T>(`${this.baseUrl}/${endpoint}`).pipe(
      catchError(this.handleError)
    );
  }
}
