import { Component } from '@angular/core';
import { RouterOutlet, Router, NavigationEnd } from '@angular/router';
import { HeaderComponent } from '../../features/page-acceuil/header/header.component';
import { FooterComponent } from '../../features/page-acceuil/footer/footer.component';
import { CommonModule } from '@angular/common';
import { filter } from 'rxjs/operators';


@Component({
  selector: 'app-beneficiaire-layout',
  imports: [ RouterOutlet , HeaderComponent, FooterComponent, CommonModule ],
  templateUrl: './beneficiaire-layout.component.html',
  styleUrl: './beneficiaire-layout.component.scss'
})
export class BeneficiaireLayoutComponent {
  showFooter = false;

  // Routes où le footer doit être affiché (pour beneficiaire-layout)
  private footerRoutes = ['/beneficiaire/beneficiaire-home'];

  constructor(private router: Router) {
    // Écouter les changements de route
    this.router.events.pipe(
      filter(event => event instanceof NavigationEnd)
    ).subscribe((event: NavigationEnd) => {
      this.showFooter = this.footerRoutes.includes(event.url);
    });

    // Vérifier la route initiale
    this.showFooter = this.footerRoutes.includes(this.router.url);
  }
}
