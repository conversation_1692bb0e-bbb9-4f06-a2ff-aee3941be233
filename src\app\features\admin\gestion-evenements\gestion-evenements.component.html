    <div class="container-fluid">
      <!-- Header -->
<div class="back-title-container">
    <span class="back-arrow" (click)="onBack()">
    ←
    </span>
    <h2 class="page-title">Gestion des événements</h2>
</div>

      <div class="container">
        <!-- Formulaire de création d'événement -->
        <mat-card class="mb-4">
          <mat-card-header>
            <mat-card-title>Créer un nouvel événement</mat-card-title>
          </mat-card-header><br>
          <mat-card-content>
            <form [formGroup]="eventForm" (ngSubmit)="onSubmit()" class="row g-3">
              <!-- Titre de l'événement -->
              <div class="col-md-6">
                <mat-form-field appearance="outline" class="w-100">
                  <mat-label>Titre de l'événement</mat-label>
                  <input matInput formControlName="titre" placeholder="Entrez le titre">
                  <mat-error *ngIf="eventForm.get('titre')?.hasError('required')">
                    Le titre est requis
                  </mat-error>
                </mat-form-field>
              </div>

              <!-- Date de début -->
              <div class="col-md-6">
                <mat-form-field appearance="outline" class="w-100">
                  <mat-label>Date de début</mat-label>
                  <input matInput [matDatepicker]="startPicker" formControlName="dateDebut">
                  <mat-datepicker-toggle matIconSuffix [for]="startPicker"></mat-datepicker-toggle>
                  <mat-datepicker #startPicker></mat-datepicker>
                  <mat-error *ngIf="eventForm.get('dateDebut')?.hasError('required')">
                    La date de début est requise
                  </mat-error>
                </mat-form-field>
              </div>

              <!-- Date de fin -->
              <div class="col-md-6">
                <mat-form-field appearance="outline" class="w-100">
                  <mat-label>Date de fin</mat-label>
                  <input matInput [matDatepicker]="endPicker" formControlName="dateFin">
                  <mat-datepicker-toggle matIconSuffix [for]="endPicker"></mat-datepicker-toggle>
                  <mat-datepicker #endPicker></mat-datepicker>
                  <mat-error *ngIf="eventForm.get('dateFin')?.hasError('required')">
                    La date de fin est requise
                  </mat-error>
                </mat-form-field>
              </div>

              <!-- Nombre de bénéficiaires -->
              <div class="col-md-6">
                <mat-form-field appearance="outline" class="w-100">
                  <mat-label>Nombre de bénéficiaire</mat-label>
                  <input matInput type="number" formControlName="nombreBeneficiaire" placeholder="0">
                  <mat-error *ngIf="eventForm.get('nombreBeneficiaire')?.hasError('required')">
                    Le nombre de bénéficiaires est requis
                  </mat-error>
                  <mat-error *ngIf="eventForm.get('nombreBeneficiaire')?.hasError('min')">
                    Le nombre doit être positif
                  </mat-error>
                </mat-form-field>
              </div>

              <!-- Description -->
              <div class="col-12">
                <mat-form-field appearance="outline" class="w-100">
                  <mat-label>Description</mat-label>
                  <textarea matInput formControlName="description" rows="3" placeholder="Description de l'événement"></textarea>
                  <mat-error *ngIf="eventForm.get('description')?.hasError('required')">
                    La description est requise
                  </mat-error>
                </mat-form-field>
              </div>

              <!-- Photo -->
              <div class="col-12">
                <div class="mb-3">
                  <label class="form-label">Photo</label>
                  <div class="border rounded p-3 text-center" style="border-style: dashed !important;">
                    <input type="file" #fileInput (change)="onFileSelected($event)" accept="image/*" class="d-none">
                    <button type="button" mat-stroked-button (click)="fileInput.click()">
                      <mat-icon>upload</mat-icon>
                      Déposer une photo
                    </button>
                    <div *ngIf="selectedFileName" class="mt-2 text-muted">
                      Fichier sélectionné: {{ selectedFileName }}
                    </div>
                  </div>
                </div>
              </div>

              <!-- Bouton de soumission -->
              <div class="col-12 text-center">
                <button mat-raised-button color="primary" type="submit" [disabled]="eventForm.invalid">
                  Créer
                </button>
              </div>
            </form>
          </mat-card-content>
        </mat-card>

        <!-- Liste des événements existants -->
        <mat-card>
          <mat-card-header>
            <mat-card-title>Événements existants</mat-card-title>
          </mat-card-header>
          <mat-card-content>
            <div *ngIf="events.length === 0" class="text-center py-4">
              <p class="text-muted">Aucun événement</p>
            </div>
            
            <div *ngIf="events.length > 0" class="table-responsive">
              <table mat-table [dataSource]="events" class="mat-elevation-z2 w-100">
                <!-- Colonne ID -->
                <ng-container matColumnDef="id">
                  <th mat-header-cell *matHeaderCellDef>Id</th>
                  <td mat-cell *matCellDef="let event">{{ event.id }}</td>
                </ng-container>

                <!-- Colonne Titre -->
                <ng-container matColumnDef="titre">
                  <th mat-header-cell *matHeaderCellDef>Titre de l'événement</th>
                  <td mat-cell *matCellDef="let event">{{ event.titre }}</td>
                </ng-container>

                <!-- Colonne Date de début -->
                <ng-container matColumnDef="dateDebut">
                  <th mat-header-cell *matHeaderCellDef>Date de début</th>
                  <td mat-cell *matCellDef="let event">{{ event.dateDebut | date:'dd/MM/yyyy' }}</td>
                </ng-container>

                <!-- Colonne Date de fin -->
                <ng-container matColumnDef="dateFin">
                  <th mat-header-cell *matHeaderCellDef>Date de fin</th>
                  <td mat-cell *matCellDef="let event">{{ event.dateFin | date:'dd/MM/yyyy' }}</td>
                </ng-container>

                <!-- Colonne Description -->
                <ng-container matColumnDef="description">
                  <th mat-header-cell *matHeaderCellDef>Description</th>
                  <td mat-cell *matCellDef="let event">{{ event.description }}</td>
                </ng-container>

                <!-- Colonne Nombre de bénéficiaires -->
                <ng-container matColumnDef="nombreBeneficiaire">
                  <th mat-header-cell *matHeaderCellDef>Nombre de bénéficiaire</th>
                  <td mat-cell *matCellDef="let event">{{ event.nombreBeneficiaire }}</td>
                </ng-container>

                <!-- Colonne Photo -->
                <ng-container matColumnDef="photo">
                  <th mat-header-cell *matHeaderCellDef>Photo</th>
                  <td mat-cell *matCellDef="let event">
                    <div class="photo-placeholder">
                      <mat-icon>image</mat-icon>
                    </div>
                  </td>
                </ng-container>

                <!-- Colonne Actions -->
                <ng-container matColumnDef="actions">
                  <th mat-header-cell *matHeaderCellDef>Actions</th>
                  <td mat-cell *matCellDef="let event">
                    <button mat-icon-button color="primary" (click)="modifierEvent(event)" matTooltip="Modifier">
                      <mat-icon>edit</mat-icon>
                    </button>
                    <button mat-icon-button color="warn" (click)="supprimerEvent(event.id)" matTooltip="Supprimer">
                      <mat-icon>delete</mat-icon>
                    </button>
                    <button mat-icon-button color="accent" (click)="voirEvent(event)" matTooltip="Voir">
                      <mat-icon>visibility</mat-icon>
                    </button>
                  </td>
                </ng-container>

                <tr mat-header-row *matHeaderRowDef="displayedColumns"></tr>
                <tr mat-row *matRowDef="let row; columns: displayedColumns;"></tr>
              </table>
            </div>
          </mat-card-content>
        </mat-card>
      </div>
    </div>

