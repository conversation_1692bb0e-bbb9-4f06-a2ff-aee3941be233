<!-- forgot-password.component.html -->
<div class="forgot-password-container">
  <div class="forgot-password-card">
    <div class="header">
      <button class="back-button" (click)="goBack()">
        <span class="material-icons">arrow_back</span>
      </button>
      <h1>Mot de passe oublié</h1>
    </div>
    
    <p class="description">
      Entrez votre adresse email pour recevoir un lien de réinitialisation de mot de passe.
    </p>

    <form [formGroup]="forgotPasswordForm" (ngSubmit)="onSubmit()">
      <div class="form-field">
        <label for="email">Adresse email</label>
        <input 
          type="email" 
          id="email" 
          formControlName="email"
          placeholder="<EMAIL>">
        <div class="error-message" *ngIf="forgotPasswordForm.get('email')?.touched && forgotPasswordForm.get('email')?.errors?.['required']">
          L'adresse email est requise
        </div>
        <div class="error-message" *ngIf="forgotPasswordForm.get('email')?.touched && forgotPasswordForm.get('email')?.errors?.['email']">
          Veuillez entrer une adresse email valide
        </div>
      </div>

      <button type="submit" class="send-button" [disabled]="forgotPasswordForm.invalid">
        Envoyer
      </button>
    </form>

    <div class="back-to-login">
      <a (click)="goToLogin()" class="login-link">Retour à la connexion</a>
    </div>
  </div>
</div>
