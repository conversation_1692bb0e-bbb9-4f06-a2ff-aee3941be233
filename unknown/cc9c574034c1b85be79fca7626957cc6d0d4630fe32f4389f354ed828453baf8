<div class="auth-container">
  <div class="auth-card">
    <div class="background-image">
      <h1>Bienvenu</h1>
    </div>
    <div class="form-container">
      <a [routerLink]="['/']" class="back-button">
        ←
      </a>
      <h2>Inscription</h2>
      <p class="subtitle">Créez votre compte pour commencer</p>
      
      <form [formGroup]="signupForm" (ngSubmit)="onSubmit()" autocomplete="off">
        <div class="form-group">
          <label for="cin">CIN</label>
          <input type="text" id="cin" formControlName="cin" placeholder="Ex: B123456" autocomplete="off" autocapitalize="off" autocorrect="off" spellcheck="false">
          <div *ngIf="signupForm.get('cin')?.invalid && (signupForm.get('cin')?.dirty || signupForm.get('cin')?.touched)" class="error-message">
            <div *ngIf="signupForm.get('cin')?.errors?.['required']">CIN est requis</div>
            <div *ngIf="signupForm.get('cin')?.errors?.['pattern']">Format CIN invalide (ex: B123456)</div>
          </div>
        </div>
        
        <div class="form-group">
          <label for="firstName">Prénom</label>
          <input type="text" id="firstName" formControlName="firstName" placeholder="Entrez votre prénom">
          <div *ngIf="signupForm.get('firstName')?.invalid && (signupForm.get('firstName')?.dirty || signupForm.get('firstName')?.touched)" class="error-message">
            <div *ngIf="signupForm.get('firstName')?.errors?.['required']">Prénom est requis</div>
          </div>
        </div>

        <div class="form-group">
          <label for="lastName">Nom</label>
          <input type="text" id="lastName" formControlName="lastName" placeholder="Entrez votre nom">
          <div *ngIf="signupForm.get('lastName')?.invalid && (signupForm.get('lastName')?.dirty || signupForm.get('lastName')?.touched)" class="error-message">
            <div *ngIf="signupForm.get('lastName')?.errors?.['required']">Nom est requis</div>
          </div>
        </div>

        <div class="form-group">
          <label for="email">Email</label>
          <input type="email" id="email" formControlName="email" placeholder="<EMAIL>" autocomplete="off">
          <div *ngIf="signupForm.get('email')?.invalid && (signupForm.get('email')?.dirty || signupForm.get('email')?.touched)" class="error-message">
            <div *ngIf="signupForm.get('email')?.errors?.['required']">Email est requis</div>
            <div *ngIf="signupForm.get('email')?.errors?.['email']">Format d'email invalide</div>
          </div>
        </div>
        
        <div class="form-group">
          <label for="phoneNumber">Téléphone</label>
          <input 
            type="tel" 
            id="phoneNumber" 
            formControlName="phoneNumber" 
            placeholder="0612345678"
          >
          <div *ngIf="signupForm.get('phoneNumber')?.invalid && (signupForm.get('phoneNumber')?.dirty || signupForm.get('phoneNumber')?.touched)" class="error-message">
            <div *ngIf="signupForm.get('phoneNumber')?.errors?.['required']">Numéro de téléphone requis</div>
            <div *ngIf="signupForm.get('phoneNumber')?.errors?.['pattern']">Format invalide (commençant par 05, 06 ou 07 suivi de 8 chiffres)</div>
          </div>
        </div>
        
        <div class="form-group">
          <label for="password">Mot de passe</label>
          <div class="password-input">
            <input [type]="hidePassword ? 'password' : 'text'" id="password" formControlName="password" placeholder="Minimum 6 caractères" autocomplete="new-password">
            <button type="button" class="toggle-password" (click)="togglePasswordVisibility()">
              <mat-icon>{{ hidePassword ? 'visibility_off' : 'visibility' }}</mat-icon>
            </button>
          </div>
          <div *ngIf="signupForm.get('password')?.invalid && (signupForm.get('password')?.dirty || signupForm.get('password')?.touched)" class="error-message">
            <div *ngIf="signupForm.get('password')?.errors?.['required']">Mot de passe est requis</div>
            <div *ngIf="signupForm.get('password')?.errors?.['minlength']">Le mot de passe doit contenir au moins 6 caractères</div>
          </div>
        </div>
        
        <div class="form-group">
          <label for="confirmPassword">Confirmer mot de passe</label>
          <div class="password-input">
            <input [type]="hideConfirmPassword ? 'password' : 'text'" id="confirmPassword" formControlName="confirmPassword" placeholder="Confirmez votre mot de passe" autocomplete="new-password">
            <button type="button" class="toggle-password" (click)="toggleConfirmPasswordVisibility()">
              <mat-icon>{{ hideConfirmPassword ? 'visibility_off' : 'visibility' }}</mat-icon>
            </button>
          </div>
          <div *ngIf="signupForm.get('confirmPassword')?.invalid && (signupForm.get('confirmPassword')?.dirty || signupForm.get('confirmPassword')?.touched)" class="error-message">
            <div *ngIf="signupForm.get('confirmPassword')?.errors?.['required']">Confirmation du mot de passe est requise</div>
            <div *ngIf="signupForm.get('confirmPassword')?.errors?.['passwordMismatch']">Les mots de passe ne correspondent pas</div>
          </div>
        </div>
        
        <div *ngIf="error" class="error-alert">
          <mat-icon class="error-icon">error</mat-icon>
          <span>{{ error }}</span>
        </div>
        
        <button type="submit" class="submit-button" [disabled]="signupForm.invalid || loading">
          {{ loading ? 'Inscription en cours...' : 'S\'inscrire' }}
        </button>
        
        <div class="auth-links">
          <p>Vous avez déjà un compte? <a routerLink="/login">Se Connecter</a></p>
        </div>
      </form>
    </div>
  </div>
</div>

