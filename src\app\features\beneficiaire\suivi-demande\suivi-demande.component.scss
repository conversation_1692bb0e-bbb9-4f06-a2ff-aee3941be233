.back-title-container {
  display: flex;
  align-items: center;
  margin-bottom: 1rem;
  gap: 0.5rem;
  padding: 0 2rem;
  margin-top: 6.5rem;
}

.back-arrow {
  font-size: 1.5rem;
  cursor: pointer;
  color: #3498db;
  transition: color 0.3s;

  &:hover {
    color: #2ecc71;
  }
}

.page-title {
  font-size: 1.5rem;
  font-weight: 600;
  color: #2c3e50;
  margin: 0;
}

.table-wrapper {
  max-width: 800px; // ou 1000px selon la taille désirée
  margin: 1.5rem auto; // centre horizontalement + espacement vertical
  padding: 2rem;
  background-color: white;
  border-radius: 16px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
}

.application-container {
  font-family: 'Segoe UI', Roboto, sans-serif;
  background-color: #f8f9fa;
  min-height: 100vh;
}

.content-container {
  padding: 2rem;
  max-width: 1200px;
  margin: 0 auto;
}

.content-title {
  font-size: 1.75rem;
  font-weight: 600;
  color: #2c3e50;
  margin-bottom: 2rem;
  position: relative;
  padding-bottom: 0.5rem;
  
  &::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 60px;
    height: 4px;
    background: linear-gradient(90deg, #3498db, #2ecc71);
    border-radius: 2px;
  }
}

.table-container {
  background: white;
  border-radius: 12px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  overflow: hidden;
}

.table-header {
  display: flex;
  background: linear-gradient(135deg, #3498db, #2ecc71);
  color: white;
  font-weight: 600;
  padding: 1rem 1.5rem;
  
  .col-2, .col-6, .col-4 {
    padding: 0.75rem 0.75rem;
  }
}

.table-row {
  display: flex;
  padding: 0.5rem 1.5rem;
  transition: all 0.3s ease;
  border-bottom: 1px solid rgba(0, 0, 0, 0.05);
  
  &:hover {
    background-color: #f8f9fa;
    transform: translateY(-2px);
  }
  
  .col-2, .col-6, .col-4 {
    padding: 0.5rem 0.5rem;
    align-self: center;
  }
  
  .col-6 {
    color: #34495e;
    font-weight: 500;
    flex: 0 0 52%;
  }
  .col-4 {
  flex: 0 0 30%;
  max-width: 30%;
}
}

.status-pill {
  display: inline-flex;
  align-items: center;
  padding: 0.5rem 1rem;
  border-radius: 50px;
  font-size: 0.85rem;
  font-weight: 600;
  text-transform: capitalize;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

  &::before {
    content: '';
    display: inline-block;
    width: 10px;
    height: 10px;
    min-width: 10px;
    min-height: 10px;
    border-radius: 50%;
    margin-right: 8px;
    flex-shrink: 0;
  }

  &.status-en-cours {
    background-color: rgba(255, 193, 7, 0.2);
    color: #ffa000;
    
    &::before {
      background-color: #ffa000;
    }
  }

  &.status-accepte {
    background-color: rgba(76, 175, 80, 0.2);
    color: #2e7d32;
    min-height: 10px;
    min-width: 10px;
    
    &::before {
      background-color: #2e7d32;
    }
  }

  &.status-refuse {
    background-color: rgba(244, 67, 54, 0.2);
    color: #c62828;
    min-height: 10px;
    min-width: 10px;
    
    &::before {
      background-color: #c62828;
    }
  }
    &.status-livre {
    background-color: rgba(76, 175, 80, 0.2);
    color: blue;
    min-height: 20px;
    min-width: 20px;
    
    &::before {
      background-color: blue;
    }
  }
}