<div class="container-fluid mt-3">
<div class="d-flex align-items-center mb-4">
<div class="back-title-container">
  <span class="back-arrow" (click)="onBack()">
    ←
  </span>
  <h2 class="page-title">Gestion des beneficiaires</h2>
</div>
</div>

    <!-- Table -->
    <div class="card">
      <div class="card-body p-0">
        <div class="table-responsive">
          <table class="table table-hover mb-0">
            <thead class="table-light">
              <tr>
                <th scope="col" class="text-center">Id</th>
                <th scope="col">Nom</th>
                <th scope="col">Prénom</th>
                <th scope="col">Adresse</th>
                <th scope="col">Date de naissance</th>
                <th scope="col">Téléphone</th>
                <th scope="col">Appareil</th>
                <th scope="col" class="text-center">Actions</th>
              </tr>
            </thead>
            <tbody>
              <tr *ngFor="let beneficiaire of beneficiaires">
                <td class="text-center">{{ beneficiaire.id }}</td>
                <td>{{ beneficiaire.nom }}</td>
                <td>{{ beneficiaire.prenom }}</td>
                <td>{{ beneficiaire.adresse }}</td>
                <td>{{ beneficiaire.dateNaissance }}</td>
                <td>{{ beneficiaire.telephone }}</td>
                <td>
                  <span class="badge" 
                        [ngClass]="{
                          'bg-warning text-dark': beneficiaire.appareil === 'Lunettes',
                          'bg-info text-dark': beneficiaire.appareil === 'Appareil auditif'
                        }">
                    {{ beneficiaire.appareil }}
                  </span>
                </td>
                <td class="text-center">
                  <button class="btn btn-outline-danger btn-sm" 
                          (click)="supprimerBeneficiaire(beneficiaire.id)">
                    Supprimer
                  </button>
                </td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>
    </div>
