import { Component, OnInit } from '@angular/core';
import { Form<PERSON>uilder, FormGroup, Validators, ReactiveFormsModule } from '@angular/forms';
import { CommonModule } from '@angular/common';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatRadioModule } from '@angular/material/radio';
import { MatButtonModule } from '@angular/material/button';
import { MatSelectModule } from '@angular/material/select';
import { MatDatepickerModule } from '@angular/material/datepicker';
import { MatNativeDateModule } from '@angular/material/core';
import { MatCardModule } from '@angular/material/card';
import { MatIconModule } from '@angular/material/icon';

@Component({
  selector: 'app-demandes',
  imports: [ CommonModule, ReactiveFormsModule, MatFormFieldModule, MatInputModule, MatRadioModule, MatButtonModule, MatSelectModule, MatDatepickerModule, MatNativeDateModule, MatCardModule, MatIconModule ],
  templateUrl: './demandes.component.html',
  styleUrl: './demandes.component.scss'
})
export class DemandesComponent implements OnInit {

    deviceForm!: FormGroup;
  uploadedFiles: any = {
    cin: null,
    amo: null,
    medical: null,
    handicap: null
  };

  constructor(private fb: FormBuilder) {}

  ngOnInit() {
    this.initForm();
  }

  initForm() {
    this.deviceForm = this.fb.group({
      nom: ['', [Validators.required]],
      prenom: ['', [Validators.required]],
      email: ['', [Validators.required, Validators.email]],
      sexe: ['masculin', [Validators.required]],
      adresse: ['', [Validators.required]],
      dateNaissance: ['', [Validators.required]],
      statut: ['ayant_droit', [Validators.required]],
      couverture: ['', [Validators.required]],
      type: ['', [Validators.required]]
    });
  }

  onFileSelect(event: any, fileType: string) {
    const file = event.target.files[0];
    if (file) {
      this.uploadedFiles[fileType] = file;
      console.log(`Fichier ${fileType} sélectionné:`, file.name);
    }
  }

  onSubmit() {
    if (this.deviceForm.valid) {
      const formData = {
        ...this.deviceForm.value,
        files: this.uploadedFiles
      };
      
      console.log('Données du formulaire:', formData);
      
      // Ici vous pouvez traiter les données du formulaire
      // Par exemple, les envoyer à un service
      alert('Formulaire soumis avec succès!');
    } else {
      console.log('Formulaire invalide');
      this.markFormGroupTouched();
    }
  }

  private markFormGroupTouched() {
    Object.keys(this.deviceForm.controls).forEach(key => {
      const control = this.deviceForm.get(key);
      control?.markAsTouched();
    });
  }
    onBack() {
  // Par exemple : revenir à la page précédente
  history.back(); // ou this.router.navigate(['/autre-page']);
}

}
