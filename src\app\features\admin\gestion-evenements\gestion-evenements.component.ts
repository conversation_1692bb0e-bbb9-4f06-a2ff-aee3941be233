import { Component } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ReactiveFormsModule, FormGroup, FormBuilder, Validators } from '@angular/forms';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatButtonModule } from '@angular/material/button';
import { MatDatepickerModule } from '@angular/material/datepicker';
import { MatNativeDateModule } from '@angular/material/core';
import { MatTableModule } from '@angular/material/table';
import { MatIconModule } from '@angular/material/icon';
import { MatCardModule } from '@angular/material/card';
import { MatToolbarModule } from '@angular/material/toolbar';

interface Event {
  id: number;
  titre: string;
  dateDebut: Date;
  dateFin: Date;
  description: string;
  nombreBeneficiaire: number;
  photo?: string;
}

@Component({
  selector: 'app-gestion-evenements',
  imports: [ CommonModule, ReactiveFormsModule, MatFormFieldModule, MatInputModule, MatButtonModule, MatDatepickerModule, MatNativeDateModule, MatTableModule, MatIconModule, MatCardModule, MatToolbarModule ],
  templateUrl: './gestion-evenements.component.html',
  styleUrl: './gestion-evenements.component.scss'
})
export class GestionEvenementsComponent {

    eventForm: FormGroup;
  events: Event[] = [
    {
      id: 1,
      titre: "Voir le monde autrement",
      dateDebut: new Date('2025-04-28'),
      dateFin: new Date('2025-06-28'),
      description: "Délégation provinciale de la santé",
      nombreBeneficiaire: 300
    },
    {
      id: 2,
      titre: "Le silence ne doit pas couper du monde",
      dateDebut: new Date('2025-04-07'),
      dateFin: new Date('2025-06-07'),
      description: "Centre hospitalier provincial de Khénifra",
      nombreBeneficiaire: 200
    }
  ];

  displayedColumns: string[] = ['id', 'titre', 'dateDebut', 'dateFin', 'description', 'nombreBeneficiaire', 'photo', 'actions'];
  selectedFileName: string = '';
  nextId: number = 3;

  constructor(private fb: FormBuilder) {
    this.eventForm = this.fb.group({
      titre: ['', Validators.required],
      dateDebut: ['', Validators.required],
      dateFin: ['', Validators.required],
      description: ['', Validators.required],
      nombreBeneficiaire: ['', [Validators.required, Validators.min(1)]]
    });
  }

  onSubmit() {
    if (this.eventForm.valid) {
      const newEvent: Event = {
        id: this.nextId++,
        titre: this.eventForm.value.titre,
        dateDebut: this.eventForm.value.dateDebut,
        dateFin: this.eventForm.value.dateFin,
        description: this.eventForm.value.description,
        nombreBeneficiaire: this.eventForm.value.nombreBeneficiaire
      };

      this.events.push(newEvent);
      this.eventForm.reset();
      this.selectedFileName = '';
      
      console.log('Événement créé:', newEvent);
    }
  }

  onFileSelected(event: any) {
    const file = event.target.files[0];
    if (file) {
      this.selectedFileName = file.name;
    }
  }

  modifierEvent(event: Event) {
    console.log('Modifier événement:', event);
    // Implémentation de la modification
  }

  supprimerEvent(id: number) {
    if (confirm('Êtes-vous sûr de vouloir supprimer cet événement ?')) {
      this.events = this.events.filter(event => event.id !== id);
      console.log('Événement supprimé, ID:', id);
    }
  }

  voirEvent(event: Event) {
    console.log('Voir événement:', event);
    // Implémentation de la visualisation
  }

  onBack() {
    console.log('Retour vers la page précédente');
    // Implémentation de la navigation de retour
  }

}
